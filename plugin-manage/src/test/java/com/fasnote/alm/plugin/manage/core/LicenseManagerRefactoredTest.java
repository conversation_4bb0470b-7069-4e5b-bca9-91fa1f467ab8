package com.fasnote.alm.plugin.manage.core;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.osgi.framework.BundleContext;

import com.fasnote.alm.plugin.manage.api.IBundleManager;
import com.fasnote.alm.plugin.manage.api.IClassLoaderManager;
import com.fasnote.alm.plugin.manage.api.ILicenseFileManager;
import com.fasnote.alm.plugin.manage.api.ILicenseValidator;
import com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager;
import com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader;
import com.fasnote.alm.plugin.manage.model.LicenseInfo;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;
import com.fasnote.alm.plugin.manage.registry.LicenseServiceRegistry;

@ExtendWith(MockitoExtension.class)
class LicenseManagerRefactoredTest {

    @Mock
    private BundleContext bundleContext;

    @Mock
    private ILicenseFileManager licenseFileManager;

    @Mock
    private ILicenseValidator licenseValidator;

    @Mock
    private IClassLoaderManager classLoaderManager;

    @Mock
    private IServiceRegistrationManager serviceRegistrationManager;

    @Mock
    private IBundleManager bundleManager;

    @Mock
    private RuntimeEnvironmentManager runtimeEnvironmentManager;

    @Mock
    private UnifiedLicenseProcessor licenseProcessor;

    @Mock
    private LicenseServiceRegistry serviceRegistry;

    @Mock
    private PluginLicense pluginLicense;

    @Mock
    private EncryptedClassLoader encryptedClassLoader;

    @Mock
    private LicenseCache licenseCache;

    private Map<String, PluginLicense> pluginLicenses;
    private LicenseManager licenseManager;

    @BeforeEach
    void setUp() {
        pluginLicenses = new ConcurrentHashMap<>();
        
        // 创建一个测试用的LicenseManager，但需要手动设置依赖
        licenseManager = spy(new LicenseManager(bundleContext));
        
        // 使用反射或者setter方法设置mock的依赖（这里简化处理）
        // 在实际测试中，可能需要使用@InjectMocks或者构造函数注入
    }

    @Test
    void testActivatePluginLicense_Success() {
        // Given
        String pluginId = "test-plugin";
        pluginLicenses.put(pluginId, pluginLicense);
        ValidationResult validationResult = ValidationResult.success("验证成功");
        
        when(licenseValidator.validateLicense(pluginLicense)).thenReturn(validationResult);
        when(licenseProcessor.getCachedLicense(pluginId)).thenReturn(licenseCache);

        // When
        ValidationResult result = licenseManager.activatePluginLicense(pluginId);

        // Then
        assertTrue(result.isValid());
        assertEquals("许可证激活成功", result.getMessage());
    }

    @Test
    void testActivatePluginLicense_LicenseNotFound() {
        // Given
        String pluginId = "non-existing-plugin";

        // When
        ValidationResult result = licenseManager.activatePluginLicense(pluginId);

        // Then
        assertFalse(result.isValid());
        assertEquals("许可证不存在: " + pluginId, result.getMessage());
    }

    @Test
    void testActivatePluginLicense_ValidationFailure() {
        // Given
        String pluginId = "test-plugin";
        pluginLicenses.put(pluginId, pluginLicense);
        ValidationResult validationResult = ValidationResult.failure("验证失败");
        
        when(licenseValidator.validateLicense(pluginLicense)).thenReturn(validationResult);

        // When
        ValidationResult result = licenseManager.activatePluginLicense(pluginId);

        // Then
        assertFalse(result.isValid());
        assertEquals("验证失败", result.getMessage());
    }

    @Test
    void testHasValidLicense() {
        // Given
        String pluginId = "test-plugin";
        when(licenseValidator.hasValidLicense(pluginId)).thenReturn(true);

        // When
        boolean result = licenseManager.hasValidLicense(pluginId);

        // Then
        assertTrue(result);
        verify(licenseValidator).hasValidLicense(pluginId);
    }

    @Test
    void testHasPluginLicense_True() {
        // Given
        String pluginId = "test-plugin";
        pluginLicenses.put(pluginId, pluginLicense);

        // When
        boolean result = licenseManager.hasPluginLicense(pluginId);

        // Then
        assertTrue(result);
    }

    @Test
    void testHasPluginLicense_False() {
        // Given
        String pluginId = "non-existing-plugin";

        // When
        boolean result = licenseManager.hasPluginLicense(pluginId);

        // Then
        assertFalse(result);
    }

    @Test
    void testGetLicenseInfo_Existing() {
        // Given
        String pluginId = "test-plugin";
        pluginLicenses.put(pluginId, pluginLicense);

        // When
        Optional<LicenseInfo> result = licenseManager.getLicenseInfo(pluginId);

        // Then
        assertTrue(result.isPresent());
    }

    @Test
    void testGetLicenseInfo_NonExisting() {
        // Given
        String pluginId = "non-existing-plugin";

        // When
        Optional<LicenseInfo> result = licenseManager.getLicenseInfo(pluginId);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void testGetPluginLicense_Existing() {
        // Given
        String pluginId = "test-plugin";
        pluginLicenses.put(pluginId, pluginLicense);

        // When
        Optional<PluginLicense> result = licenseManager.getPluginLicense(pluginId);

        // Then
        assertTrue(result.isPresent());
        assertEquals(pluginLicense, result.get());
    }

    @Test
    void testGetPluginLicense_NonExisting() {
        // Given
        String pluginId = "non-existing-plugin";

        // When
        Optional<PluginLicense> result = licenseManager.getPluginLicense(pluginId);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void testGetEncryptedClassLoader() {
        // Given
        String pluginId = "test-plugin";
        Optional<EncryptedClassLoader> expectedResult = Optional.of(encryptedClassLoader);
        
        when(runtimeEnvironmentManager.getEncryptedClassLoader(pluginId)).thenReturn(expectedResult);

        // When
        Optional<EncryptedClassLoader> result = licenseManager.getEncryptedClassLoader(pluginId);

        // Then
        assertTrue(result.isPresent());
        assertEquals(encryptedClassLoader, result.get());
        verify(runtimeEnvironmentManager).getEncryptedClassLoader(pluginId);
    }

    @Test
    void testCreateServiceInstanceFromLicense() {
        // Given
        Class<?> serviceInterface = String.class;
        Object expectedInstance = "test-service";
        
        when(serviceRegistrationManager.createServiceInstanceFromLicense(serviceInterface)).thenReturn(expectedInstance);

        // When
        Object result = licenseManager.createServiceInstanceFromLicense(serviceInterface);

        // Then
        assertEquals(expectedInstance, result);
        verify(serviceRegistrationManager).createServiceInstanceFromLicense(serviceInterface);
    }

    @Test
    void testGetRegisteredPluginIds() {
        // Given
        pluginLicenses.put("plugin1", pluginLicense);
        pluginLicenses.put("plugin2", pluginLicense);

        // When
        List<String> result = licenseManager.getRegisteredPluginIds();

        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains("plugin1"));
        assertTrue(result.contains("plugin2"));
    }

    @Test
    void testGetServiceRegistry() {
        // When
        LicenseServiceRegistry result = licenseManager.getServiceRegistry();

        // Then
        assertNotNull(result);
    }

    @Test
    void testGetStatistics() {
        // Given
        pluginLicenses.put("plugin1", pluginLicense);
        when(classLoaderManager.getClassLoaderCount()).thenReturn(1);
        when(runtimeEnvironmentManager.getRuntimeEnvironmentCount()).thenReturn(1);
        when(serviceRegistrationManager.getServiceStatistics()).thenReturn(Map.of("services", 5));

        // When
        Map<String, Object> result = licenseManager.getStatistics();

        // Then
        assertEquals(1, result.get("registeredPlugins"));
        assertEquals(1, result.get("activeClassLoaders"));
        assertEquals(1, result.get("runtimeEnvironments"));
        assertEquals(5, result.get("services"));
        assertNotNull(result.get("lastUpdate"));
    }

    @Test
    void testIsFeatureEnabled() {
        // Given
        String featureName = "test-feature";
        when(licenseValidator.isFeatureEnabled(featureName)).thenReturn(true);

        // When
        boolean result = licenseManager.isFeatureEnabled(featureName);

        // Then
        assertTrue(result);
        verify(licenseValidator).isFeatureEnabled(featureName);
    }

    @Test
    void testRemovePluginLicense() {
        // Given
        String pluginId = "test-plugin";
        pluginLicenses.put(pluginId, pluginLicense);

        // When
        licenseManager.removePluginLicense(pluginId);

        // Then
        assertFalse(pluginLicenses.containsKey(pluginId));
        verify(runtimeEnvironmentManager).cleanupRuntimeEnvironment(pluginId);
        verify(classLoaderManager).cleanupClassLoader(pluginId);
        verify(serviceRegistrationManager).cleanupPluginServices(pluginId);
    }

    @Test
    void testRefreshAllLicenses() {
        // Given
        Map<String, ValidationResult> validationResults = Map.of(
            "plugin1", ValidationResult.success("有效"),
            "plugin2", ValidationResult.failure("无效")
        );
        when(licenseValidator.validateAllLicenses()).thenReturn(validationResults);

        // When
        licenseManager.refreshAllLicenses();

        // Then
        verify(licenseValidator).validateAllLicenses();
    }

    @Test
    void testCleanup() {
        // Given
        pluginLicenses.put("plugin1", pluginLicense);

        // When
        licenseManager.cleanup();

        // Then
        assertTrue(pluginLicenses.isEmpty());
        verify(runtimeEnvironmentManager).cleanupAllRuntimeEnvironments();
        verify(classLoaderManager).cleanupAllClassLoaders();
        verify(serviceRegistrationManager).cleanupAllServices();
    }
}
