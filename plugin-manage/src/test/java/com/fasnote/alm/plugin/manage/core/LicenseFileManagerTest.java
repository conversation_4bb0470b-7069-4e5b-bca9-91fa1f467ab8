package com.fasnote.alm.plugin.manage.core;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;

@ExtendWith(MockitoExtension.class)
class LicenseFileManagerTest {

    @Mock
    private UnifiedLicenseProcessor licenseProcessor;

    @Mock
    private RuntimeEnvironmentManager runtimeEnvironmentManager;

    @Mock
    private LicenseCache licenseCache;

    @Mock
    private PluginLicense pluginLicense;

    private Map<String, PluginLicense> pluginLicenses;
    private LicenseFileManager licenseFileManager;

    @BeforeEach
    void setUp() {
        pluginLicenses = new ConcurrentHashMap<>();
        licenseFileManager = new LicenseFileManager(pluginLicenses, licenseProcessor, runtimeEnvironmentManager);
    }

    @Test
    void testAutoScanAndLoadLicenses() {
        // Given
        // 由于autoScanAndLoadLicenses方法依赖于文件系统操作，这里主要测试方法不抛异常

        // When & Then
        assertDoesNotThrow(() -> licenseFileManager.autoScanAndLoadLicenses());
    }

    @Test
    void testLoadAndActivateLicenseFromFile_Success() {
        // Given
        String pluginId = "test-plugin";
        ValidationResult expectedResult = ValidationResult.success("成功");
        
        when(licenseProcessor.loadAndProcessLicense(eq(pluginId), any(String.class))).thenReturn(expectedResult);
        when(licenseProcessor.getCachedLicense(pluginId)).thenReturn(licenseCache);
        when(licenseCache.getLicenseMetadata()).thenReturn(pluginLicense);

        // When
        ValidationResult result = licenseFileManager.loadAndActivateLicenseFromFile(pluginId);

        // Then
        assertTrue(result.isValid());
        assertEquals("许可证加载并激活成功", result.getMessage());
    }

    @Test
    void testLoadAndActivateLicenseFromFile_FileNotFound() {
        // Given
        String pluginId = "non-existing-plugin";

        // When
        ValidationResult result = licenseFileManager.loadAndActivateLicenseFromFile(pluginId);

        // Then
        assertFalse(result.isValid());
        assertTrue(result.getMessage().contains("未找到许可证文件") || result.getMessage().contains("失败"));
    }

    @Test
    void testRegisterPluginLicense_Success() {
        // Given
        String pluginId = "test-plugin";
        byte[] licenseData = "test-license-data".getBytes();
        ValidationResult expectedResult = ValidationResult.success("成功");
        
        when(licenseProcessor.loadAndProcessLicense(pluginId, licenseData)).thenReturn(expectedResult);
        when(licenseProcessor.getCachedLicense(pluginId)).thenReturn(licenseCache);
        when(licenseCache.getLicenseMetadata()).thenReturn(pluginLicense);

        // When
        ValidationResult result = licenseFileManager.registerPluginLicense(pluginId, licenseData);

        // Then
        assertTrue(result.isValid());
        assertEquals(pluginLicense, pluginLicenses.get(pluginId));
        verify(runtimeEnvironmentManager).setupRuntimeEnvironment(pluginId, licenseCache);
    }

    @Test
    void testRegisterPluginLicense_Failure() {
        // Given
        String pluginId = "test-plugin";
        byte[] licenseData = "invalid-license-data".getBytes();
        ValidationResult expectedResult = ValidationResult.failure("验证失败");
        
        when(licenseProcessor.loadAndProcessLicense(pluginId, licenseData)).thenReturn(expectedResult);

        // When
        ValidationResult result = licenseFileManager.registerPluginLicense(pluginId, licenseData);

        // Then
        assertFalse(result.isValid());
        assertEquals("验证失败", result.getMessage());
        assertNull(pluginLicenses.get(pluginId));
        verify(runtimeEnvironmentManager, never()).setupRuntimeEnvironment(any(), any());
    }

    @Test
    void testPreloadPluginLicense_Success() {
        // Given
        String pluginId = "test-plugin";
        byte[] licenseData = "test-license-data".getBytes();
        ValidationResult expectedResult = ValidationResult.success("成功");
        
        when(licenseProcessor.loadAndProcessLicense(pluginId, licenseData)).thenReturn(expectedResult);
        when(licenseProcessor.getCachedLicense(pluginId)).thenReturn(licenseCache);
        when(licenseCache.getLicenseMetadata()).thenReturn(pluginLicense);

        // When
        ValidationResult result = licenseFileManager.preloadPluginLicense(pluginId, licenseData);

        // Then
        assertTrue(result.isValid());
        assertEquals(pluginLicense, pluginLicenses.get(pluginId));
        verify(runtimeEnvironmentManager).setupRuntimeEnvironment(pluginId, licenseCache);
    }

    @Test
    void testPreloadPluginLicense_Failure() {
        // Given
        String pluginId = "test-plugin";
        byte[] licenseData = "invalid-license-data".getBytes();
        ValidationResult expectedResult = ValidationResult.failure("验证失败");
        
        when(licenseProcessor.loadAndProcessLicense(pluginId, licenseData)).thenReturn(expectedResult);

        // When
        ValidationResult result = licenseFileManager.preloadPluginLicense(pluginId, licenseData);

        // Then
        assertFalse(result.isValid());
        assertEquals("验证失败", result.getMessage());
        assertNull(pluginLicenses.get(pluginId));
        verify(runtimeEnvironmentManager, never()).setupRuntimeEnvironment(any(), any());
    }

    @Test
    void testPreloadPluginLicenseFromFile_Success() {
        // Given
        String pluginId = "test-plugin";
        String licenseFilePath = "/path/to/license.lic";
        ValidationResult expectedResult = ValidationResult.success("成功");
        
        when(licenseProcessor.loadAndProcessLicense(pluginId, licenseFilePath)).thenReturn(expectedResult);
        when(licenseProcessor.getCachedLicense(pluginId)).thenReturn(licenseCache);
        when(licenseCache.getLicenseMetadata()).thenReturn(pluginLicense);

        // When
        ValidationResult result = licenseFileManager.preloadPluginLicenseFromFile(pluginId, licenseFilePath);

        // Then
        assertTrue(result.isValid());
        assertEquals(pluginLicense, pluginLicenses.get(pluginId));
        verify(runtimeEnvironmentManager).setupRuntimeEnvironment(pluginId, licenseCache);
    }

    @Test
    void testPreloadPluginLicenseFromFile_Failure() {
        // Given
        String pluginId = "test-plugin";
        String licenseFilePath = "/path/to/invalid-license.lic";
        ValidationResult expectedResult = ValidationResult.failure("文件无效");
        
        when(licenseProcessor.loadAndProcessLicense(pluginId, licenseFilePath)).thenReturn(expectedResult);

        // When
        ValidationResult result = licenseFileManager.preloadPluginLicenseFromFile(pluginId, licenseFilePath);

        // Then
        assertFalse(result.isValid());
        assertEquals("文件无效", result.getMessage());
        assertNull(pluginLicenses.get(pluginId));
        verify(runtimeEnvironmentManager, never()).setupRuntimeEnvironment(any(), any());
    }

    @Test
    void testRescanLicenseFiles() {
        // Given
        // 由于rescanLicenseFiles方法依赖于文件系统操作，这里主要测试方法不抛异常并返回合理值

        // When
        int result = licenseFileManager.rescanLicenseFiles();

        // Then
        assertTrue(result >= 0); // 应该返回非负数
    }

    @Test
    void testScanLicenseFiles() {
        // Given
        // 由于scanLicenseFiles方法依赖于文件系统操作，这里主要测试方法不抛异常

        // When
        Map<String, String> result = licenseFileManager.scanLicenseFiles();

        // Then
        assertNotNull(result);
        // 在测试环境中，可能返回空的Map，这是正常的
    }

    @Test
    void testRegisterPluginLicense_NullCache() {
        // Given
        String pluginId = "test-plugin";
        byte[] licenseData = "test-license-data".getBytes();
        ValidationResult expectedResult = ValidationResult.success("成功");
        
        when(licenseProcessor.loadAndProcessLicense(pluginId, licenseData)).thenReturn(expectedResult);
        when(licenseProcessor.getCachedLicense(pluginId)).thenReturn(null); // 返回null缓存

        // When
        ValidationResult result = licenseFileManager.registerPluginLicense(pluginId, licenseData);

        // Then
        assertTrue(result.isValid());
        assertNull(pluginLicenses.get(pluginId)); // 由于缓存为null，不应该添加到pluginLicenses
        verify(runtimeEnvironmentManager, never()).setupRuntimeEnvironment(any(), any());
    }

    @Test
    void testPreloadPluginLicense_NullCache() {
        // Given
        String pluginId = "test-plugin";
        byte[] licenseData = "test-license-data".getBytes();
        ValidationResult expectedResult = ValidationResult.success("成功");
        
        when(licenseProcessor.loadAndProcessLicense(pluginId, licenseData)).thenReturn(expectedResult);
        when(licenseProcessor.getCachedLicense(pluginId)).thenReturn(null); // 返回null缓存

        // When
        ValidationResult result = licenseFileManager.preloadPluginLicense(pluginId, licenseData);

        // Then
        assertTrue(result.isValid());
        assertNull(pluginLicenses.get(pluginId)); // 由于缓存为null，不应该添加到pluginLicenses
        verify(runtimeEnvironmentManager, never()).setupRuntimeEnvironment(any(), any());
    }
}
