package com.fasnote.alm.plugin.manage.core;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;

@ExtendWith(MockitoExtension.class)
class LicenseValidatorTest {

    @Mock
    private SecurityValidator securityValidator;

    private Map<String, PluginLicense> pluginLicenses;
    private LicenseValidator licenseValidator;

    @BeforeEach
    void setUp() {
        pluginLicenses = new ConcurrentHashMap<>();
        licenseValidator = new LicenseValidator(pluginLicenses, securityValidator);
    }

    @Test
    void testInitialize_Success() throws Exception {
        // Given
        doNothing().when(securityValidator).initialize();

        // When
        licenseValidator.initialize();

        // Then
        assertTrue(licenseValidator.isInitialized());
        verify(securityValidator).initialize();
    }

    @Test
    void testInitialize_Failure() throws Exception {
        // Given
        doThrow(new RuntimeException("初始化失败")).when(securityValidator).initialize();

        // When & Then
        assertThrows(Exception.class, () -> licenseValidator.initialize());
        assertFalse(licenseValidator.isInitialized());
    }

    @Test
    void testValidateLicense_NullLicense() {
        // When
        ValidationResult result = licenseValidator.validateLicense(null);

        // Then
        assertFalse(result.isValid());
        assertEquals("许可证为空", result.getMessage());
    }

    @Test
    void testValidateLicense_ValidLicense() throws Exception {
        // Given
        PluginLicense license = mock(PluginLicense.class);
        ValidationResult expectedResult = ValidationResult.success("验证成功");
        
        when(securityValidator.validateLicense(license)).thenReturn(expectedResult);
        licenseValidator.initialize();

        // When
        ValidationResult result = licenseValidator.validateLicense(license);

        // Then
        assertTrue(result.isValid());
        assertEquals("验证成功", result.getMessage());
        verify(securityValidator).validateLicense(license);
    }

    @Test
    void testValidateLicense_InvalidLicense() throws Exception {
        // Given
        PluginLicense license = mock(PluginLicense.class);
        ValidationResult expectedResult = ValidationResult.failure("许可证无效");
        
        when(securityValidator.validateLicense(license)).thenReturn(expectedResult);
        licenseValidator.initialize();

        // When
        ValidationResult result = licenseValidator.validateLicense(license);

        // Then
        assertFalse(result.isValid());
        assertEquals("许可证无效", result.getMessage());
    }

    @Test
    void testHasValidLicense_ExistingValidLicense() throws Exception {
        // Given
        String pluginId = "test-plugin";
        PluginLicense license = mock(PluginLicense.class);
        pluginLicenses.put(pluginId, license);
        
        when(securityValidator.validateLicense(license)).thenReturn(ValidationResult.success("有效"));
        licenseValidator.initialize();

        // When
        boolean result = licenseValidator.hasValidLicense(pluginId);

        // Then
        assertTrue(result);
    }

    @Test
    void testHasValidLicense_ExistingInvalidLicense() throws Exception {
        // Given
        String pluginId = "test-plugin";
        PluginLicense license = mock(PluginLicense.class);
        pluginLicenses.put(pluginId, license);
        
        when(securityValidator.validateLicense(license)).thenReturn(ValidationResult.failure("无效"));
        licenseValidator.initialize();

        // When
        boolean result = licenseValidator.hasValidLicense(pluginId);

        // Then
        assertFalse(result);
    }

    @Test
    void testHasValidLicense_NonExistingLicense() {
        // Given
        String pluginId = "non-existing-plugin";

        // When
        boolean result = licenseValidator.hasValidLicense(pluginId);

        // Then
        assertFalse(result);
    }

    @Test
    void testValidateAllLicenses() throws Exception {
        // Given
        PluginLicense license1 = mock(PluginLicense.class);
        PluginLicense license2 = mock(PluginLicense.class);
        pluginLicenses.put("plugin1", license1);
        pluginLicenses.put("plugin2", license2);
        
        when(securityValidator.validateLicense(license1)).thenReturn(ValidationResult.success("有效"));
        when(securityValidator.validateLicense(license2)).thenReturn(ValidationResult.failure("无效"));
        licenseValidator.initialize();

        // When
        Map<String, ValidationResult> results = licenseValidator.validateAllLicenses();

        // Then
        assertEquals(2, results.size());
        assertTrue(results.get("plugin1").isValid());
        assertFalse(results.get("plugin2").isValid());
    }

    @Test
    void testIsFeatureEnabled_WithValidLicense() throws Exception {
        // Given
        String featureName = "test-feature";
        PluginLicense license = mock(PluginLicense.class);
        pluginLicenses.put("plugin1", license);
        
        when(securityValidator.validateLicense(license)).thenReturn(ValidationResult.success("有效"));
        licenseValidator.initialize();

        // When
        boolean result = licenseValidator.isFeatureEnabled(featureName);

        // Then
        assertTrue(result);
    }

    @Test
    void testIsFeatureEnabled_WithoutValidLicense() throws Exception {
        // Given
        String featureName = "test-feature";
        PluginLicense license = mock(PluginLicense.class);
        pluginLicenses.put("plugin1", license);
        
        when(securityValidator.validateLicense(license)).thenReturn(ValidationResult.failure("无效"));
        licenseValidator.initialize();

        // When
        boolean result = licenseValidator.isFeatureEnabled(featureName);

        // Then
        assertFalse(result);
    }

    @Test
    void testGetValidationStatistics() throws Exception {
        // Given
        PluginLicense validLicense = mock(PluginLicense.class);
        PluginLicense invalidLicense = mock(PluginLicense.class);
        pluginLicenses.put("valid-plugin", validLicense);
        pluginLicenses.put("invalid-plugin", invalidLicense);
        
        when(securityValidator.validateLicense(validLicense)).thenReturn(ValidationResult.success("有效"));
        when(securityValidator.validateLicense(invalidLicense)).thenReturn(ValidationResult.failure("无效"));
        licenseValidator.initialize();

        // When
        Map<String, Object> stats = licenseValidator.getValidationStatistics();

        // Then
        assertEquals(2, stats.get("totalLicenses"));
        assertEquals(1, stats.get("validLicenses"));
        assertEquals(1, stats.get("invalidLicenses"));
        assertEquals(0.5, stats.get("validationRate"));
        assertTrue((Boolean) stats.get("initialized"));
        assertNotNull(stats.get("lastUpdate"));
    }

    @Test
    void testSetSecurityValidator() {
        // Given
        SecurityValidator newValidator = mock(SecurityValidator.class);

        // When
        licenseValidator.setSecurityValidator(newValidator);

        // Then
        assertEquals(newValidator, licenseValidator.getSecurityValidator());
        assertFalse(licenseValidator.isInitialized()); // 应该重置初始化状态
    }
}
