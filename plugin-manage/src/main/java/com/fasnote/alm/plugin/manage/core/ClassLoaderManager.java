package com.fasnote.alm.plugin.manage.core;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import org.osgi.framework.Bundle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.license.crypto.RSAKeyManager;
import com.fasnote.alm.license.crypto.RSALicenseEncryption;
import com.fasnote.alm.plugin.manage.api.IBundleManager;
import com.fasnote.alm.plugin.manage.api.IClassLoaderManager;
import com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader;
import com.fasnote.alm.plugin.manage.model.PluginLicense;

/**
 * 类加载器管理器实现
 * 负责加密类加载器的创建和管理
 */
public class ClassLoaderManager implements IClassLoaderManager {

    private static final Logger logger = LoggerFactory.getLogger(ClassLoaderManager.class);

    // 加密类加载器映射（插件ID -> 类加载器）
    private final Map<String, EncryptedClassLoader> classLoaders = new ConcurrentHashMap<>();

    // 已加载的插件许可证缓存
    private final Map<String, PluginLicense> pluginLicenses;

    // Bundle管理器
    private final IBundleManager bundleManager;

    // 统一许可证处理器
    private final UnifiedLicenseProcessor licenseProcessor;

    /**
     * 构造函数
     *
     * @param pluginLicenses   插件许可证缓存
     * @param bundleManager    Bundle管理器
     * @param licenseProcessor 统一许可证处理器
     */
    public ClassLoaderManager(Map<String, PluginLicense> pluginLicenses,
                              IBundleManager bundleManager,
                              UnifiedLicenseProcessor licenseProcessor) {
        this.pluginLicenses = pluginLicenses;
        this.bundleManager = bundleManager;
        this.licenseProcessor = licenseProcessor;
    }

    @Override
    public Optional<EncryptedClassLoader> createEncryptedClassLoaderForPlugin(String pluginId) {
        PluginLicense license = pluginLicenses.get(pluginId);
        if (license == null) {
            logger.warn("未找到插件许可证: {}", pluginId);
            return Optional.empty();
        }

        if (!license.hasEncryptedClasses()) {
            logger.debug("插件许可证中没有加密类数据: {}", pluginId);
            return Optional.empty();
        }

        try {
            // 优先使用缓存中的数据
            LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
            if (cache != null) {
                return createEncryptedClassLoaderFromCache(pluginId, cache);
            }

            // 如果缓存不可用，尝试从文件路径创建
            String licenseFilePath = license.getLicenseFilePath();
            if (licenseFilePath == null || licenseFilePath.trim().isEmpty()) {
                logger.warn("插件许可证缺少文件路径: {}", pluginId);
                return Optional.empty();
            }
            return createEncryptedClassLoaderFromFile(pluginId, licenseFilePath);
        } catch (Exception e) {
            logger.error("为插件创建加密类加载器失败: {}", pluginId, e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<EncryptedClassLoader> getEncryptedClassLoader(String pluginId) {
        if (pluginId == null || pluginId.trim().isEmpty()) {
            logger.warn("插件ID为空，无法获取类加载器");
            return Optional.empty();
        }

        EncryptedClassLoader classLoader = classLoaders.get(pluginId);
        if (classLoader == null) {
            logger.debug("未找到插件的加密类加载器: {}", pluginId);
        }

        return Optional.ofNullable(classLoader);
    }

    @Override
    public ClassLoader getBusinessPluginClassLoader(String pluginId) {
        try {
            // 查找对应的Bundle
            Bundle targetBundle = bundleManager.findBundleByPluginId(pluginId);
            if (targetBundle == null) {
                logger.warn("未找到Bundle: {} (插件: {})", pluginId, pluginId);
                return getClass().getClassLoader();
            }

            // 获取Bundle的ClassLoader
            ClassLoader bundleClassLoader = bundleManager.getBundleClassLoader(targetBundle);
            if (bundleClassLoader != null) {
                logger.info("成功获取业务插件ClassLoader: {} -> {}", pluginId, pluginId);
                return bundleClassLoader;
            } else {
                logger.warn("无法获取Bundle的ClassLoader: {}", pluginId);
                return getClass().getClassLoader();
            }

        } catch (Exception e) {
            logger.error("获取业务插件ClassLoader失败: {}", pluginId, e);
            return getClass().getClassLoader();
        }
    }

    @Override
    public void cleanupClassLoader(String pluginId) {
        logger.info("清理插件类加载器: {}", pluginId);

        try {
            EncryptedClassLoader classLoader = classLoaders.remove(pluginId);
            if (classLoader != null) {
                classLoader.clearCache();
                logger.info("已清理插件类加载器: {}", pluginId);
            } else {
                logger.debug("插件类加载器不存在，无需清理: {}", pluginId);
            }
        } catch (Exception e) {
            logger.error("清理插件类加载器失败: {}", pluginId, e);
        }
    }

    @Override
    public void cleanupAllClassLoaders() {
        logger.info("清理所有类加载器");

        try {
            for (Map.Entry<String, EncryptedClassLoader> entry : classLoaders.entrySet()) {
                String pluginId = entry.getKey();
                EncryptedClassLoader classLoader = entry.getValue();
                try {
                    classLoader.clearCache();
                    logger.info("已清理插件类加载器: {}", pluginId);
                } catch (Exception e) {
                    logger.error("清理插件类加载器失败: {}", pluginId, e);
                }
            }
            classLoaders.clear();
            logger.info("所有类加载器清理完成");
        } catch (Exception e) {
            logger.error("清理所有类加载器失败", e);
        }
    }

    @Override
    public int getClassLoaderCount() {
        return classLoaders.size();
    }

    /**
     * 从许可证缓存创建加密类加载器（主要实现）
     * 用于加载许可证中的加密实现类
     *
     * @param pluginId 插件ID
     * @param cache    许可证缓存
     * @return 加密类加载器的Optional包装，如果无加密类则返回empty
     */
    private Optional<EncryptedClassLoader> createEncryptedClassLoaderFromCache(String pluginId, LicenseCache cache) {
        try {
            // 检查是否包含加密类
            if (!cache.hasEncryptedClasses()) {
                logger.info("许可证中未包含加密类数据，将使用标准类加载器: {}", pluginId);
                return Optional.empty();
            }

            // 直接使用缓存中的解密结果
            Map<String, byte[]> decryptedClasses = cache.getDecryptedClasses();
            byte[] decryptedJarData = createJarFromClasses(decryptedClasses);

            // 获取业务插件Bundle的ClassLoader作为父类加载器
            ClassLoader businessPluginClassLoader = getBusinessPluginClassLoader(pluginId);
            logger.info("为插件 {} 使用ClassLoader: {}", pluginId, businessPluginClassLoader.getClass().getName());

            EncryptedClassLoader classLoader = new EncryptedClassLoader(businessPluginClassLoader);
            // 添加解密后的JAR数据到类加载器
            classLoader.addDecryptedJar("license-classes.jar", decryptedJarData);
            
            // 缓存类加载器
            classLoaders.put(pluginId, classLoader);
            
            logger.info("成功创建加密类加载器: {}", pluginId);
            return Optional.of(classLoader);

        } catch (Exception e) {
            logger.error("创建加密类加载器失败: {}", pluginId, e);
            return Optional.empty();
        }
    }

    /**
     * 从许可证文件路径创建加密类加载器（使用crypto模块的文件路径接口）
     * 用于加载许可证中的加密实现类
     *
     * @param pluginId        插件ID
     * @param licenseFilePath 许可证文件路径
     * @return 加密类加载器的Optional包装，如果无加密类则返回empty
     */
    private Optional<EncryptedClassLoader> createEncryptedClassLoaderFromFile(String pluginId, String licenseFilePath) {
        try {
            // 获取插件许可证对象
            PluginLicense license = pluginLicenses.get(pluginId);
            if (license == null || !license.hasEncryptedClasses()) {
                logger.info("许可证中未包含加密类数据，将使用标准类加载器: {}", pluginId);
                return Optional.empty();
            }

            // 使用crypto模块的文件路径接口直接解密许可证文件
            byte[] decryptedJarData = decryptLicenseFile(licenseFilePath);

            // 获取业务插件Bundle的ClassLoader作为父类加载器
            ClassLoader businessPluginClassLoader = getBusinessPluginClassLoader(pluginId);
            logger.info("为插件 {} 使用ClassLoader: {}", pluginId, businessPluginClassLoader.getClass().getName());

            EncryptedClassLoader classLoader = new EncryptedClassLoader(businessPluginClassLoader);
            // 添加解密后的JAR数据到类加载器
            classLoader.addDecryptedJar("license-classes.jar", decryptedJarData);
            
            // 缓存类加载器
            classLoaders.put(pluginId, classLoader);
            
            logger.info("成功创建加密类加载器: {}", pluginId);
            return Optional.of(classLoader);

        } catch (Exception e) {
            logger.error("创建加密类加载器失败: {}", pluginId, e);
            return Optional.empty();
        }
    }

    /**
     * 将类字节码打包成JAR
     */
    private byte[] createJarFromClasses(Map<String, byte[]> classes) {
        try {
            java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();

            try (java.util.jar.JarOutputStream jos = new java.util.jar.JarOutputStream(baos)) {
                for (Map.Entry<String, byte[]> entry : classes.entrySet()) {
                    String className = entry.getKey();
                    byte[] classBytes = entry.getValue();

                    // 转换类名为路径格式
                    String classPath = className.replace('.', '/') + ".class";

                    java.util.jar.JarEntry jarEntry = new java.util.jar.JarEntry(classPath);
                    jarEntry.setSize(classBytes.length);

                    jos.putNextEntry(jarEntry);
                    jos.write(classBytes);
                    jos.closeEntry();
                }
            }

            return baos.toByteArray();
        } catch (Exception e) {
            logger.error("创建JAR包失败", e);
            throw new RuntimeException("创建JAR包失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用crypto模块统一接口解密许可证文件
     *
     * @param licenseFilePath 许可证文件路径
     * @return 解密后的JAR数据
     * @throws Exception 解密失败时抛出异常
     */
    private byte[] decryptLicenseFile(String licenseFilePath) throws Exception {
        logger.debug("开始解密许可证文件: {}", licenseFilePath);

        // 使用crypto模块的统一接口（文件路径接口）
        RSAKeyManager keyManager = new RSAKeyManager(); // 使用硬编码公钥
        RSALicenseEncryption rsaEncryption = new RSALicenseEncryption(keyManager);

        try {
            // 直接解密并验证许可证文件（使用文件路径接口）
            Map<String, byte[]> decryptedClasses = rsaEncryption.decryptAndVerifyLicenseFile(licenseFilePath);

            // 将解密的类数据转换为JAR格式
            return createJarFromClasses(decryptedClasses);
        } catch (Exception e) {
            logger.error("解密许可证失败: {}", e.getMessage(), e);
            throw new RuntimeException("解密许可证失败: " + e.getMessage(), e);
        }
    }
}
