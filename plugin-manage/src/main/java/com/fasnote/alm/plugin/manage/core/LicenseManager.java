package com.fasnote.alm.plugin.manage.core;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import org.osgi.framework.Bundle;
import org.osgi.framework.BundleContext;
import org.osgi.framework.wiring.BundleWiring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.license.crypto.RSAKeyManager;
import com.fasnote.alm.license.crypto.RSALicenseEncryption;
import com.fasnote.alm.plugin.manage.api.IBundleManager;
import com.fasnote.alm.plugin.manage.api.IClassLoaderManager;
import com.fasnote.alm.plugin.manage.api.ILicenseFileManager;
import com.fasnote.alm.plugin.manage.api.ILicenseManager;
import com.fasnote.alm.plugin.manage.api.ILicenseValidator;
import com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager;
import com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager;

import com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader;
import com.fasnote.alm.plugin.manage.model.LicenseInfo;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;
import com.fasnote.alm.plugin.manage.registry.LicenseServiceRegistry;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;

/**
 * 运行时许可证管理器（重构后的协调器）
 * 负责协调各个管理器组件，不再包含具体实现逻辑
 *
 * 核心功能：
 * 1. 协调许可证文件管理
 * 2. 协调许可证验证
 * 3. 协调类加载器管理
 * 4. 协调服务注册管理
 * 5. 协调Bundle管理
 */
public class LicenseManager implements ILicenseManager {

	private static final Logger logger = LoggerFactory.getLogger(LicenseManager.class);

	// 已加载的插件许可证缓存（共享给各个管理器）
	private final Map<String, PluginLicense> pluginLicenses;

	// 各个管理器组件
	private final ILicenseFileManager licenseFileManager;
	private final ILicenseValidator licenseValidator;
	private final IClassLoaderManager classLoaderManager;
	private final IServiceRegistrationManager serviceRegistrationManager;
	private final IBundleManager bundleManager;
	private final IRuntimeEnvironmentManager runtimeEnvironmentManager;

	// 统一许可证处理器
	private final UnifiedLicenseProcessor licenseProcessor;

	// 许可证服务注册表
	private final LicenseServiceRegistry serviceRegistry;

	// OSGi Bundle上下文
	private final BundleContext bundleContext;

	// 初始化状态标志
	private volatile boolean initialized = false;

	/**
	 * 构造函数（完整依赖注入）
	 *
	 * @param bundleContext OSGi Bundle上下文
	 * @param pluginLicenses 插件许可证缓存
	 * @param bundleManager Bundle管理器
	 * @param classLoaderManager 类加载器管理器
	 * @param licenseValidator 许可证验证器
	 * @param serviceRegistrationManager 服务注册管理器
	 * @param runtimeEnvironmentManager 运行时环境管理器
	 * @param licenseFileManager 许可证文件管理器
	 * @param licenseProcessor 统一许可证处理器
	 * @param serviceRegistry 许可证服务注册表
	 */
	public LicenseManager(
			BundleContext bundleContext,
			Map<String, PluginLicense> pluginLicenses,
			IBundleManager bundleManager,
			IClassLoaderManager classLoaderManager,
			ILicenseValidator licenseValidator,
			IServiceRegistrationManager serviceRegistrationManager,
			IRuntimeEnvironmentManager runtimeEnvironmentManager,
			ILicenseFileManager licenseFileManager,
			UnifiedLicenseProcessor licenseProcessor,
			LicenseServiceRegistry serviceRegistry) {

		if (bundleContext == null) {
			throw new IllegalArgumentException("BundleContext不能为null");
		}
		if (pluginLicenses == null) {
			throw new IllegalArgumentException("PluginLicenses不能为null");
		}
		if (bundleManager == null) {
			throw new IllegalArgumentException("BundleManager不能为null");
		}
		if (classLoaderManager == null) {
			throw new IllegalArgumentException("ClassLoaderManager不能为null");
		}
		if (licenseValidator == null) {
			throw new IllegalArgumentException("LicenseValidator不能为null");
		}
		if (serviceRegistrationManager == null) {
			throw new IllegalArgumentException("ServiceRegistrationManager不能为null");
		}
		if (runtimeEnvironmentManager == null) {
			throw new IllegalArgumentException("RuntimeEnvironmentManager不能为null");
		}
		if (licenseFileManager == null) {
			throw new IllegalArgumentException("LicenseFileManager不能为null");
		}
		if (licenseProcessor == null) {
			throw new IllegalArgumentException("LicenseProcessor不能为null");
		}
		if (serviceRegistry == null) {
			throw new IllegalArgumentException("ServiceRegistry不能为null");
		}

		// 设置所有依赖
		this.bundleContext = bundleContext;
		this.pluginLicenses = pluginLicenses;
		this.bundleManager = bundleManager;
		this.classLoaderManager = classLoaderManager;
		this.licenseValidator = licenseValidator;
		this.serviceRegistrationManager = serviceRegistrationManager;
		this.runtimeEnvironmentManager = runtimeEnvironmentManager;
		this.licenseFileManager = licenseFileManager;
		this.licenseProcessor = licenseProcessor;
		this.serviceRegistry = serviceRegistry;

		logger.info("LicenseManager创建完成（依赖注入模式），BundleContext: {}", bundleContext.getBundle().getSymbolicName());

		// 在依赖注入模式下，不需要手动初始化，各个管理器已经通过DI容器正确初始化
		// initialize(); // 移除手动初始化
	}

	/**
	 * 构造函数（兼容性，手动创建依赖）
	 *
	 * @param bundleContext OSGi Bundle上下文（必填）
	 */
	public LicenseManager(BundleContext bundleContext) {
		if (bundleContext == null) {
			throw new IllegalArgumentException("BundleContext不能为null");
		}

		// 设置Bundle上下文
		this.bundleContext = bundleContext;

		// 初始化pluginLicenses
		this.pluginLicenses = new ConcurrentHashMap<>();

		// 初始化基础组件
		this.licenseProcessor = new UnifiedLicenseProcessor();
		this.serviceRegistry = new LicenseServiceRegistry();

		// 按依赖顺序创建各个管理器组件
		this.bundleManager = new BundleManager(bundleContext);
		this.classLoaderManager = new ClassLoaderManager(pluginLicenses, bundleManager, licenseProcessor);
		this.licenseValidator = new LicenseValidator(pluginLicenses);

		// 先创建ServiceRegistrationManager（不依赖RuntimeEnvironmentManager）
		this.serviceRegistrationManager = new ServiceRegistrationManager(pluginLicenses, serviceRegistry, null);

		// 再创建RuntimeEnvironmentManager
		this.runtimeEnvironmentManager = new RuntimeEnvironmentManager(classLoaderManager, serviceRegistrationManager, licenseProcessor);

		// 最后创建LicenseFileManager
		this.licenseFileManager = new LicenseFileManager(pluginLicenses, licenseProcessor, runtimeEnvironmentManager);

		// 设置ServiceRegistrationManager的RuntimeEnvironmentManager引用
		if (serviceRegistrationManager instanceof ServiceRegistrationManager) {
			((ServiceRegistrationManager) serviceRegistrationManager).setRuntimeEnvironmentManager(runtimeEnvironmentManager);
		}

		// 初始化验证器
		try {
			licenseValidator.initialize();
		} catch (Exception e) {
			logger.error("LicenseValidator 初始化失败", e);
		}

		logger.info("LicenseManager创建完成，BundleContext: {}", bundleContext.getBundle().getSymbolicName());

		// 立即初始化，因为BundleContext已经可用
		initialize();
	}

	/**
	 * 构造函数（支持依赖注入）
	 *
	 * @param securityValidator 安全验证器
	 */
	public LicenseManager(SecurityValidator securityValidator) {
		// 设置Bundle上下文为null（延迟初始化）
		this.bundleContext = null;

		// 初始化pluginLicenses
		this.pluginLicenses = new ConcurrentHashMap<>();

		// 初始化基础组件
		this.licenseProcessor = new UnifiedLicenseProcessor();
		this.serviceRegistry = new LicenseServiceRegistry();

		// 创建各个管理器组件（延迟初始化Bundle相关组件）
		this.bundleManager = null; // 延迟初始化
		this.classLoaderManager = new ClassLoaderManager(pluginLicenses, bundleManager, licenseProcessor);
		this.licenseValidator = new LicenseValidator(pluginLicenses, securityValidator);

		// 先创建ServiceRegistrationManager（不依赖RuntimeEnvironmentManager）
		this.serviceRegistrationManager = new ServiceRegistrationManager(pluginLicenses, serviceRegistry, null);

		// 再创建RuntimeEnvironmentManager
		this.runtimeEnvironmentManager = new RuntimeEnvironmentManager(classLoaderManager, serviceRegistrationManager, licenseProcessor);

		// 最后创建LicenseFileManager
		this.licenseFileManager = new LicenseFileManager(pluginLicenses, licenseProcessor, runtimeEnvironmentManager);

		// 设置ServiceRegistrationManager的RuntimeEnvironmentManager引用
		if (serviceRegistrationManager instanceof ServiceRegistrationManager) {
			((ServiceRegistrationManager) serviceRegistrationManager).setRuntimeEnvironmentManager(runtimeEnvironmentManager);
		}

		// 延迟初始化，等待BundleContext设置后再初始化
		logger.info("LicenseManager创建完成（带参数），等待BundleContext设置后初始化");
	}

	@Override
	public ValidationResult activatePluginLicense(String pluginId) {
		logger.info("激活插件许可证: {}", pluginId);

		// 确保已初始化
		ensureInitialized();

		try {
			Optional<PluginLicense> licenseOpt = getPluginLicense(pluginId);
			if (!licenseOpt.isPresent()) {
				return ValidationResult.failure("许可证不存在: " + pluginId);
			}

			PluginLicense license = licenseOpt.get();

			// 委托给验证器进行验证
			ValidationResult validationResult = licenseValidator.validateLicense(license);
			if (!validationResult.isValid()) {
				return validationResult;
			}

			// 委托给运行时环境管理器设置运行时环境（加载实现类）
			LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
			if (cache != null) {
				runtimeEnvironmentManager.setupRuntimeEnvironment(pluginId, cache);
			} else if (license.getRawLicenseData() != null) {
				// 如果缓存中没有，使用原始数据（兼容性处理）
				try {
					runtimeEnvironmentManager.setupRuntimeEnvironment(pluginId, license.getRawLicenseData().getBytes("UTF-8"));
				} catch (Exception e) {
					logger.error("转换许可证数据失败: {}", pluginId, e);
				}
			}

			logger.info("插件许可证激活成功: {}", pluginId);
			return ValidationResult.success("许可证激活成功");

		} catch (Exception e) {
			String errorMessage = "许可证激活失败: " + e.getMessage();
			logger.error(errorMessage, e);
			return ValidationResult.failure(errorMessage);
		}
	}

	/**
	 * 自动扫描并预加载许可证文件（仅解密验证，不加载实现类）
	 */
	private void autoScanAndLoadLicenses() {
		// 委托给许可证文件管理器
		licenseFileManager.autoScanAndLoadLicenses();
	}

	@Override
	public void cleanup() {
		logger.info("清理许可证管理器资源");

		try {
			// 委托给运行时环境管理器清理类加载器
			if (runtimeEnvironmentManager != null) {
				runtimeEnvironmentManager.cleanupAllRuntimeEnvironments();
			}

			// 委托给类加载器管理器清理
			if (classLoaderManager != null) {
				classLoaderManager.cleanupAllClassLoaders();
			}

			// 委托给服务注册管理器清理
			if (serviceRegistrationManager != null) {
				serviceRegistrationManager.cleanupAllServices();
			}

			// 清理许可证缓存
			pluginLicenses.clear();

			logger.info("许可证管理器资源清理完成");

		} catch (Exception e) {
			logger.error("清理许可证管理器资源失败", e);
		}
	}



	/**
	 * 为指定插件创建加密类加载器
	 *
	 * @param pluginId 插件ID
	 * @return 加密类加载器的Optional包装，如果无加密类则返回empty
	 */
	public Optional<EncryptedClassLoader> createEncryptedClassLoaderForPlugin(String pluginId) {
		PluginLicense license = pluginLicenses.get(pluginId);
		if (license == null) {
			logger.warn("未找到插件许可证: {}", pluginId);
			return Optional.empty();
		}

		if (!license.hasEncryptedClasses()) {
			logger.debug("插件许可证中没有加密类数据: {}", pluginId);
			return Optional.empty();
		}

		try {
			// 获取许可证文件路径
			String licenseFilePath = license.getLicenseFilePath();
			if (licenseFilePath == null || licenseFilePath.trim().isEmpty()) {
				logger.warn("插件许可证缺少文件路径: {}", pluginId);
				return Optional.empty();
			}
			return createEncryptedClassLoaderFromFile(pluginId, licenseFilePath);
		} catch (Exception e) {
			logger.error("为插件创建加密类加载器失败: {}", pluginId, e);
			return Optional.empty();
		}
	}

	/**
	 * 从许可证文件路径创建加密类加载器（使用crypto模块的文件路径接口） 用于加载许可证中的加密实现类
	 *
	 * @param pluginId        插件ID
	 * @param licenseFilePath 许可证文件路径
	 * @return 加密类加载器的Optional包装，如果无加密类则返回empty
	 */
	private Optional<EncryptedClassLoader> createEncryptedClassLoaderFromFile(String pluginId, String licenseFilePath) {
		try {
			// 获取插件许可证对象
			PluginLicense license = pluginLicenses.get(pluginId);
			if (license == null || !license.hasEncryptedClasses()) {
				logger.info("许可证中未包含加密类数据，将使用标准类加载器: {}", pluginId);
				return Optional.empty();
			}

			// 使用crypto模块的文件路径接口直接解密许可证文件
			byte[] decryptedJarData = decryptLicenseFile(licenseFilePath);

			// 获取业务插件Bundle的ClassLoader作为父类加载器
			ClassLoader businessPluginClassLoader = getBusinessPluginClassLoader(pluginId);
			logger.info("为插件 {} 使用ClassLoader: {}", pluginId, businessPluginClassLoader.getClass().getName());

			EncryptedClassLoader classLoader = new EncryptedClassLoader(businessPluginClassLoader);
			// 添加解密后的JAR数据到类加载器
			classLoader.addDecryptedJar("license-classes.jar", decryptedJarData);
			logger.info("成功创建加密类加载器: {}", pluginId);
			return Optional.of(classLoader);

		} catch (Exception e) {
			logger.error("创建加密类加载器失败: {}", pluginId, e);
			return Optional.empty();
		}
	}



	/**
	 * 将类字节码打包成JAR
	 */
	private byte[] createJarFromClasses(Map<String, byte[]> classes) {
		try {
			java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();

			try (java.util.jar.JarOutputStream jos = new java.util.jar.JarOutputStream(baos)) {
				for (Map.Entry<String, byte[]> entry : classes.entrySet()) {
					String className = entry.getKey();
					byte[] classBytes = entry.getValue();

					// 转换类名为路径格式
					String classPath = className.replace('.', '/') + ".class";

					java.util.jar.JarEntry jarEntry = new java.util.jar.JarEntry(classPath);
					jarEntry.setSize(classBytes.length);

					jos.putNextEntry(jarEntry);
					jos.write(classBytes);
					jos.closeEntry();
				}
			}

			return baos.toByteArray();
		} catch (Exception e) {
			logger.error("创建JAR包失败", e);
			throw new RuntimeException("创建JAR包失败: " + e.getMessage(), e);
		}
	}

	@Override
	public Object createServiceInstanceFromLicense(Class<?> serviceInterface) {
		// 委托给服务注册管理器
		return serviceRegistrationManager.createServiceInstanceFromLicense(serviceInterface);
	}

	/**
	 * 使用crypto模块统一接口解密许可证文件
	 *
	 * @param licenseFilePath 许可证文件路径
	 * @return 解密后的JAR数据
	 * @throws Exception 解密失败时抛出异常
	 */
	private byte[] decryptLicenseFile(String licenseFilePath) throws Exception {
		logger.debug("开始解密许可证文件: {}", licenseFilePath);

		// 使用crypto模块的统一接口（文件路径接口）
		RSAKeyManager keyManager = new RSAKeyManager(); // 使用硬编码公钥
		RSALicenseEncryption rsaEncryption = new RSALicenseEncryption(keyManager);

		try {
			// 直接解密并验证许可证文件（使用文件路径接口）
			Map<String, byte[]> decryptedClasses = rsaEncryption.decryptAndVerifyLicenseFile(licenseFilePath);

			// 将解密的类数据转换为JAR格式
			return createJarFromClasses(decryptedClasses);
		} catch (Exception e) {
			logger.error("解密许可证失败: {}", e.getMessage(), e);
			throw new RuntimeException("解密许可证失败: " + e.getMessage(), e);
		}
	}





	/**
	 * 根据符号名称查找Bundle
	 *
	 * @param symbolicName Bundle符号名称
	 * @return 找到的Bundle，如果未找到则返回null
	 */
	private Bundle findBundleBySymbolicName(String symbolicName) {
		if (bundleContext == null) {
			return null;
		}

		Bundle[] bundles = bundleContext.getBundles();
		logger.debug("查找Bundle: {}，当前共有 {} 个Bundle", symbolicName, bundles.length);

		// 先列出所有相关的Bundle（用于调试）
		for (Bundle bundle : bundles) {
			String bundleName = bundle.getSymbolicName();
			if (bundleName != null && (bundleName.contains("fasnote") || bundleName.contains("feishu"))) {
				logger.debug("相关Bundle: {} (状态: {})", bundleName, getBundleStateName(bundle.getState()));
			}
		}

		for (Bundle bundle : bundles) {
			if (symbolicName.equals(bundle.getSymbolicName())) {
				// 检查Bundle状态 - 允许STARTING状态，因为ClassLoader通常已经可用
				if (bundle.getState() == Bundle.ACTIVE || bundle.getState() == Bundle.RESOLVED
						|| bundle.getState() == Bundle.STARTING) {
					logger.info("找到Bundle: {} (状态: {})", symbolicName, getBundleStateName(bundle.getState()));
					return bundle;
				} else {
					logger.warn("Bundle状态不正确: {} (状态: {})", symbolicName, getBundleStateName(bundle.getState()));
					return bundle; // 即使状态不理想，也尝试使用它
				}
			}
		}

		logger.warn("未找到Bundle: {}", symbolicName);
		return null;
	}

	/**
	 * 获取Bundle状态名称
	 *
	 * @param state Bundle状态
	 * @return 状态名称
	 */
	private String getBundleStateName(int state) {
		switch (state) {
		case Bundle.UNINSTALLED:
			return "UNINSTALLED";
		case Bundle.INSTALLED:
			return "INSTALLED";
		case Bundle.RESOLVED:
			return "RESOLVED";
		case Bundle.STARTING:
			return "STARTING";
		case Bundle.STOPPING:
			return "STOPPING";
		case Bundle.ACTIVE:
			return "ACTIVE";
		default:
			return "UNKNOWN(" + state + ")";
		}
	}

	/**
	 * 获取业务插件Bundle的ClassLoader
	 *
	 * @param pluginId 插件ID
	 * @return 业务插件Bundle的ClassLoader，如果未找到则返回当前ClassLoader
	 */
	private ClassLoader getBusinessPluginClassLoader(String pluginId) {
		try {
			// 如果没有BundleContext，返回当前ClassLoader
			if (bundleContext == null) {
				logger.warn("BundleContext未设置，使用当前ClassLoader: {}", pluginId);
				return getClass().getClassLoader();
			}

			// 查找对应的Bundle
			Bundle targetBundle = findBundleBySymbolicName(pluginId);
			if (targetBundle == null) {
				logger.warn("未找到Bundle: {} (插件: {})", pluginId, pluginId);
				return getClass().getClassLoader();
			}

			// 获取Bundle的ClassLoader
			BundleWiring bundleWiring = targetBundle.adapt(BundleWiring.class);
			if (bundleWiring != null) {
				ClassLoader bundleClassLoader = bundleWiring.getClassLoader();
				logger.info("成功获取业务插件ClassLoader: {} -> {}", pluginId, pluginId);
				return bundleClassLoader;
			} else {
				logger.warn("无法获取Bundle的ClassLoader: {}", pluginId);
				return getClass().getClassLoader();
			}

		} catch (Exception e) {
			logger.error("获取业务插件ClassLoader失败: {}", pluginId, e);
			return getClass().getClassLoader();
		}
	}

	@Override
	public Optional<EncryptedClassLoader> getEncryptedClassLoader(String pluginId) {
		// 委托给运行时环境管理器
		return runtimeEnvironmentManager.getEncryptedClassLoader(pluginId);
	}

	@Override
	public Optional<LicenseInfo> getLicenseInfo(String pluginId) {
		Optional<PluginLicense> licenseOpt = getPluginLicense(pluginId);
		if (!licenseOpt.isPresent()) {
			logger.debug("未找到插件许可证信息: {}", pluginId);
			return Optional.empty();
		}

		PluginLicense license = licenseOpt.get();
		LicenseInfo info = new LicenseInfo();
		info.setPluginId(pluginId);
		info.setLicenseData(license.getLicenseData());
		info.setLoadTime(LocalDateTime.now());

		return Optional.of(info);
	}

	@Override
	public Optional<PluginLicense> getPluginLicense(String pluginId) {
		if (pluginId == null || pluginId.trim().isEmpty()) {
			logger.warn("插件ID为空，无法获取许可证");
			return Optional.empty();
		}

		// 确保已初始化
		ensureInitialized();

		PluginLicense license = pluginLicenses.get(pluginId);
		if (license == null) {
			logger.debug("未找到插件许可证: {}", pluginId);
		}

		return Optional.ofNullable(license);
	}

	@Override
	public List<String> getRegisteredPluginIds() {
		return new java.util.ArrayList<>(pluginLicenses.keySet());
	}

	@Override
	public LicenseServiceRegistry getServiceRegistry() {
		return serviceRegistry;
	}

	@Override
	public Map<String, Object> getStatistics() {
		Map<String, Object> stats = new ConcurrentHashMap<>();
		stats.put("registeredPlugins", pluginLicenses.size());
		stats.put("activeClassLoaders", classLoaderManager != null ? classLoaderManager.getClassLoaderCount() : 0);
		stats.put("runtimeEnvironments", runtimeEnvironmentManager != null ? runtimeEnvironmentManager.getRuntimeEnvironmentCount() : 0);
		stats.put("lastUpdate", LocalDateTime.now());

		// 合并各个管理器的统计信息
		if (serviceRegistrationManager != null) {
			Map<String, Object> serviceStats = serviceRegistrationManager.getServiceStatistics();
			stats.putAll(serviceStats);
		}

		if (licenseValidator != null) {
			Map<String, Object> validationStats = ((LicenseValidator) licenseValidator).getValidationStatistics();
			stats.putAll(validationStats);
		}

		return stats;
	}

	@Override
	public boolean hasPluginLicense(String pluginId) {
		return pluginLicenses.containsKey(pluginId);
	}

	@Override
	public boolean hasValidLicense(String pluginId) {
		// 委托给验证器
		return licenseValidator.hasValidLicense(pluginId);
	}

	/**
	 * 确保许可证管理器已初始化
	 */
	private void ensureInitialized() {
		if (!initialized) {
			initialize();
		}
	}

	/**
	 * 初始化许可证管理器
	 */
	private void initialize() {
		if (initialized) {
			logger.debug("许可证管理器已经初始化，跳过重复初始化");
			return;
		}

		logger.info("初始化许可证管理器");

		// 自动扫描并加载许可证文件
		autoScanAndLoadLicenses();

		initialized = true;
		logger.info("许可证管理器初始化完成");
	}

	@Override
	public boolean isFeatureEnabled(String featureName) {
		// 委托给验证器
		return ((LicenseValidator) licenseValidator).isFeatureEnabled(featureName);
	}

	/**
	 * 检查Bundle是否需要许可证验证
	 *
	 * @param bundle   Bundle对象
	 * @param pluginId 插件ID（用于日志）
	 * @return 是否需要许可证验证
	 */
	public boolean isPluginRequiresLicense(org.osgi.framework.Bundle bundle, String pluginId) {
		// 委托给Bundle管理器
		return bundleManager != null ? bundleManager.isPluginRequiresLicense(bundle, pluginId) : false;
	}

	/**
	 * 检查插件是否需要许可证验证
	 *
	 * @param pluginId 插件ID
	 * @return 是否需要许可证验证
	 */
	public boolean isPluginRequiresLicense(String pluginId) {
		// 委托给Bundle管理器
		return bundleManager != null ? bundleManager.isPluginRequiresLicense(pluginId) : false;
	}

	/**
	 * 从文件加载并激活许可证
	 *
	 * @param pluginId 插件ID
	 * @return 激活结果
	 */
	public ValidationResult loadAndActivateLicenseFromFile(String pluginId) {
		// 委托给许可证文件管理器
		return licenseFileManager.loadAndActivateLicenseFromFile(pluginId);
	}







	/**
	 * 预加载插件许可证（仅解密验证，不加载实现类）
	 *
	 * @param pluginId    插件ID
	 * @param licenseData 许可证数据（加密）
	 * @return 注册结果
	 */
	public ValidationResult preloadPluginLicense(String pluginId, byte[] licenseData) {
		// 委托给许可证文件管理器
		return licenseFileManager.preloadPluginLicense(pluginId, licenseData);
	}

	/**
	 * 从文件预加载插件许可证（仅解密验证，不加载实现类）
	 *
	 * @param pluginId        插件ID
	 * @param licenseFilePath 许可证文件路径
	 * @return 注册结果
	 */
	public ValidationResult preloadPluginLicenseFromFile(String pluginId, String licenseFilePath) {
		// 委托给许可证文件管理器
		return licenseFileManager.preloadPluginLicenseFromFile(pluginId, licenseFilePath);
	}

	@Override
	public void refreshAllLicenses() {
		logger.info("刷新所有许可证状态");

		// 委托给验证器进行批量验证
		if (licenseValidator != null) {
			Map<String, ValidationResult> results = ((LicenseValidator) licenseValidator).validateAllLicenses();

			for (Map.Entry<String, ValidationResult> entry : results.entrySet()) {
				String pluginId = entry.getKey();
				ValidationResult result = entry.getValue();
				logger.info("插件 {} 许可证状态: {}", pluginId, result.isValid() ? "有效" : "无效");
			}
		}

		logger.info("所有许可证状态刷新完成");
	}

	/**
	 * 注册插件许可证（完整注册，包括加载实现类）
	 *
	 * @param pluginId    插件ID
	 * @param licenseData 许可证数据（加密）
	 * @return 验证结果
	 */
	public ValidationResult registerPluginLicense(String pluginId, byte[] licenseData) {
		// 委托给许可证文件管理器
		return licenseFileManager.registerPluginLicense(pluginId, licenseData);
	}



	@Override
	public void removePluginLicense(String pluginId) {
		logger.info("移除插件许可证: {}", pluginId);

		// 清理运行时环境
		if (runtimeEnvironmentManager != null) {
			runtimeEnvironmentManager.cleanupRuntimeEnvironment(pluginId);
		}

		// 清理类加载器
		if (classLoaderManager != null) {
			classLoaderManager.cleanupClassLoader(pluginId);
		}

		// 清理服务注册
		if (serviceRegistrationManager != null) {
			serviceRegistrationManager.cleanupPluginServices(pluginId);
		}

		// 清理许可证缓存
		pluginLicenses.remove(pluginId);

		logger.info("插件许可证移除完成: {}", pluginId);
	}

	/**
	 * 重新扫描并加载新的许可证文件
	 *
	 * @return 新加载的许可证数量
	 */
	public int rescanLicenseFiles() {
		// 委托给许可证文件管理器
		return licenseFileManager.rescanLicenseFiles();
	}

	/**
	 * 设置安全验证器（用于依赖注入）
	 *
	 * @param securityValidator 安全验证器
	 */
	public void setSecurityValidator(SecurityValidator securityValidator) {
		// 委托给许可证验证器
		if (licenseValidator != null) {
			licenseValidator.setSecurityValidator(securityValidator);
		}
	}





	/**
	 * 验证许可证
	 *
	 * @param license 许可证对象
	 * @return 验证结果
	 */
	public ValidationResult validateLicense(PluginLicense license) {
		// 委托给许可证验证器
		return licenseValidator.validateLicense(license);
	}
}