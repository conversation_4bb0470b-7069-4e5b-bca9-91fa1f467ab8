package com.fasnote.alm.plugin.manage.core;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager;
import com.fasnote.alm.plugin.manage.api.LicenseAware;
import com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.registry.LicenseServiceRegistry;

/**
 * 服务注册管理器实现
 * 负责服务的注册和管理
 */
public class ServiceRegistrationManager implements IServiceRegistrationManager {

    private static final Logger logger = LoggerFactory.getLogger(ServiceRegistrationManager.class);

    // 已加载的插件许可证缓存
    private final Map<String, PluginLicense> pluginLicenses;

    // 许可证服务注册表
    private final LicenseServiceRegistry serviceRegistry;

    // 服务统计信息
    private final Map<String, Integer> serviceStatistics = new ConcurrentHashMap<>();

    /**
     * 构造函数
     *
     * @param pluginLicenses  插件许可证缓存
     * @param serviceRegistry 许可证服务注册表
     */
    public ServiceRegistrationManager(Map<String, PluginLicense> pluginLicenses,
                                      LicenseServiceRegistry serviceRegistry) {
        this.pluginLicenses = pluginLicenses;
        this.serviceRegistry = serviceRegistry;
    }

    @Override
    public void loadAndRegisterImplementations(String pluginId, LicenseCache cache, EncryptedClassLoader classLoader) {
        try {
            PluginLicense license = cache.getLicenseMetadata();
            if (license == null || !license.hasServiceMappings()) {
                logger.info("插件 {} 的许可证中未定义服务映射，跳过服务注册", pluginId);
                return;
            }

            logger.info("开始注册许可证服务: {}", pluginId);

            // 遍历许可证中定义的服务映射
            Map<String, String> serviceMappings = license.getServiceMappings();
            int successCount = 0;
            int totalCount = serviceMappings.size();

            for (Map.Entry<String, String> mapping : serviceMappings.entrySet()) {
                String interfaceName = mapping.getKey();
                String implementationName = mapping.getValue();

                try {
                    // 从许可证的加密类加载器中加载服务接口
                    Class<?> serviceInterface = classLoader.loadClass(interfaceName);

                    // 使用createImplementationInstanceFromLicense创建服务实例
                    try {
                        Object serviceInstance = createImplementationInstanceFromLicense(pluginId, license,
                                serviceInterface, implementationName, classLoader);

                        if (serviceInstance != null) {
                            // 注册服务到服务注册表
                            registerServiceInstance(serviceInterface, serviceInstance, interfaceName, implementationName,
                                    pluginId);
                            successCount++;
                        }

                    } catch (Exception e) {
                        logger.warn("创建服务实例失败，跳过注册: {} -> {} (插件: {})", interfaceName, implementationName, pluginId, e);
                    }

                } catch (ClassNotFoundException e) {
                    logger.error("加载许可证中的服务类失败: {} -> {} (插件: {})", interfaceName, implementationName, pluginId, e);
                } catch (Exception e) {
                    logger.error("注册许可证服务失败: {} -> {} (插件: {})", interfaceName, implementationName, pluginId, e);
                }
            }

            // 更新统计信息
            serviceStatistics.put(pluginId, successCount);

            logger.info("许可证服务注册完成: {}，成功 {}/{}", pluginId, successCount, totalCount);

        } catch (Exception e) {
            logger.error("从缓存加载和注册许可证实现类失败: {}", pluginId, e);
        }
    }

    @Override
    public Object createImplementationInstanceFromLicense(String pluginId, PluginLicense license,
                                                          Class<?> serviceInterface, String implementationName,
                                                          EncryptedClassLoader classLoader) throws Exception {

        try {
            // 加载实现类
            Class<?> implementationClass = classLoader.loadClass(implementationName);

            // 验证实现类确实实现了服务接口
            if (!serviceInterface.isAssignableFrom(implementationClass)) {
                logger.warn("实现类{}未实现接口{}", implementationName, serviceInterface.getName());
                return null;
            }

            // 创建实例
            Object instance = implementationClass.getDeclaredConstructor().newInstance();

            // 如果实例实现了LicenseAware接口，注入许可证信息
            if (instance instanceof LicenseAware) {
                ((LicenseAware) instance).setLicenseInfo(license);
            }

            logger.info("成功从许可证创建服务实例: {} -> {} (插件: {})", serviceInterface.getName(), implementationName, pluginId);

            return instance;

        } catch (ClassNotFoundException e) {
            logger.warn("许可证中未找到实现类: {} (插件: {})", implementationName, pluginId);
            return null;
        } catch (Exception e) {
            logger.error("创建许可证服务实例失败: {} (插件: {})", implementationName, pluginId, e);
            return null;
        }
    }

    @Override
    public void registerServiceInstance(Class<?> serviceInterface, Object serviceInstance, String interfaceName,
                                       String implementationName, String pluginId) {
        try {
            // 按接口类型注册服务
            @SuppressWarnings("unchecked")
            Class<Object> genericInterface = (Class<Object>) serviceInterface;
            serviceRegistry.registerService(genericInterface, serviceInstance);

            // 按服务名称注册服务（格式：插件ID.接口名）
            String serviceName = pluginId + "." + interfaceName;
            serviceRegistry.registerService(serviceName, serviceInstance);

            logger.info("成功注册许可证服务: {} -> {} (插件: {})", interfaceName, implementationName, pluginId);

        } catch (Exception e) {
            logger.error("注册服务实例失败: {} -> {} (插件: {})", interfaceName, implementationName, pluginId, e);
            throw e; // 重新抛出异常，让调用者处理
        }
    }



    @Override
    public Map<String, Object> getServiceStatistics() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        stats.put("totalPlugins", pluginLicenses.size());
        stats.put("registeredServices", serviceStatistics.values().stream().mapToInt(Integer::intValue).sum());
        stats.put("pluginServiceCounts", new ConcurrentHashMap<>(serviceStatistics));
        stats.put("lastUpdate", java.time.LocalDateTime.now());
        return stats;
    }

    @Override
    public void cleanupPluginServices(String pluginId) {
        logger.info("清理插件服务注册: {}", pluginId);

        try {
            // 移除统计信息
            serviceStatistics.remove(pluginId);

            // 注意：这里不直接清理serviceRegistry中的服务，因为可能被其他地方使用
            // 实际的服务清理应该由serviceRegistry自己管理

            logger.info("插件服务注册清理完成: {}", pluginId);

        } catch (Exception e) {
            logger.error("清理插件服务注册失败: {}", pluginId, e);
        }
    }

    @Override
    public void cleanupAllServices() {
        logger.info("清理所有服务注册");

        try {
            // 清理统计信息
            serviceStatistics.clear();

            logger.info("所有服务注册清理完成");

        } catch (Exception e) {
            logger.error("清理所有服务注册失败", e);
        }
    }

    /**
     * 获取插件许可证
     *
     * @param pluginId 插件ID
     * @return 许可证对象的Optional包装，如果不存在则返回empty
     */
    private Optional<PluginLicense> getPluginLicense(String pluginId) {
        if (pluginId == null || pluginId.trim().isEmpty()) {
            logger.warn("插件ID为空，无法获取许可证");
            return Optional.empty();
        }

        PluginLicense license = pluginLicenses.get(pluginId);
        if (license == null) {
            logger.debug("未找到插件许可证: {}", pluginId);
        }

        return Optional.ofNullable(license);
    }

    /**
     * 获取所有已注册的插件ID
     *
     * @return 插件ID列表
     */
    private List<String> getRegisteredPluginIds() {
        return new java.util.ArrayList<>(pluginLicenses.keySet());
    }

    /**
     * 获取许可证服务注册表
     *
     * @return 服务注册表实例
     */
    public LicenseServiceRegistry getServiceRegistry() {
        return serviceRegistry;
    }


}
