package com.fasnote.alm.plugin.manage.core;

import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.api.ILicenseValidator;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;

/**
 * 许可证验证器实现
 * 负责许可证验证逻辑
 */
public class LicenseValidator implements ILicenseValidator {

    private static final Logger logger = LoggerFactory.getLogger(LicenseValidator.class);

    // 安全验证器
    private SecurityValidator securityValidator;

    // 已加载的插件许可证缓存
    private final Map<String, PluginLicense> pluginLicenses;

    // 初始化状态标志
    private volatile boolean initialized = false;

    /**
     * 构造函数
     *
     * @param pluginLicenses    插件许可证缓存
     * @param securityValidator 安全验证器
     */
    public LicenseValidator(Map<String, PluginLicense> pluginLicenses, SecurityValidator securityValidator) {
        this.pluginLicenses = pluginLicenses;
        this.securityValidator = securityValidator;
    }

    /**
     * 构造函数（延迟初始化安全验证器）
     *
     * @param pluginLicenses 插件许可证缓存
     */
    public LicenseValidator(Map<String, PluginLicense> pluginLicenses) {
        this.pluginLicenses = pluginLicenses;
        this.securityValidator = new SecurityValidator();
    }

    @Override
    public void initialize() throws Exception {
        if (initialized) {
            logger.debug("许可证验证器已经初始化，跳过重复初始化");
            return;
        }

        logger.info("初始化许可证验证器");

        try {
            if (securityValidator == null) {
                securityValidator = new SecurityValidator();
            }
            securityValidator.initialize();
            initialized = true;
            logger.info("许可证验证器初始化完成");
        } catch (Exception e) {
            logger.error("许可证验证器初始化失败", e);
            throw e;
        }
    }

    @Override
    public boolean isInitialized() {
        return initialized;
    }

    @Override
    public ValidationResult validateLicense(PluginLicense license) {
        if (license == null) {
            return ValidationResult.failure("许可证为空");
        }

        // 确保验证器已初始化
        if (!initialized) {
            try {
                initialize();
            } catch (Exception e) {
                logger.error("验证器初始化失败", e);
                return ValidationResult.failure("验证器初始化失败: " + e.getMessage());
            }
        }

        // 使用安全验证器进行验证
        return securityValidator.validateLicense(license);
    }

    @Override
    public boolean hasValidLicense(String pluginId) {
        Optional<PluginLicense> licenseOpt = getPluginLicense(pluginId);
        if (!licenseOpt.isPresent()) {
            logger.debug("插件许可证不存在: {}", pluginId);
            return false;
        }

        ValidationResult result = validateLicense(licenseOpt.get());
        return result.isValid();
    }

    /**
     * 获取插件许可证
     *
     * @param pluginId 插件ID
     * @return 许可证对象的Optional包装，如果不存在则返回empty
     */
    private Optional<PluginLicense> getPluginLicense(String pluginId) {
        if (pluginId == null || pluginId.trim().isEmpty()) {
            logger.warn("插件ID为空，无法获取许可证");
            return Optional.empty();
        }

        PluginLicense license = pluginLicenses.get(pluginId);
        if (license == null) {
            logger.debug("未找到插件许可证: {}", pluginId);
        }

        return Optional.ofNullable(license);
    }

    /**
     * 批量验证许可证
     *
     * @return 验证结果映射（插件ID -> 验证结果）
     */
    public Map<String, ValidationResult> validateAllLicenses() {
        logger.info("开始批量验证所有许可证");

        Map<String, ValidationResult> results = new java.util.HashMap<>();

        for (Map.Entry<String, PluginLicense> entry : pluginLicenses.entrySet()) {
            String pluginId = entry.getKey();
            PluginLicense license = entry.getValue();

            try {
                ValidationResult result = validateLicense(license);
                results.put(pluginId, result);
                logger.debug("插件 {} 许可证验证结果: {}", pluginId, result.isValid() ? "有效" : "无效");
            } catch (Exception e) {
                logger.error("验证插件许可证失败: {}", pluginId, e);
                results.put(pluginId, ValidationResult.failure("验证异常: " + e.getMessage()));
            }
        }

        logger.info("批量验证完成，共验证 {} 个许可证", results.size());
        return results;
    }

    /**
     * 检查功能是否可用
     *
     * @param featureName 功能名称
     * @return 是否可用
     */
    public boolean isFeatureEnabled(String featureName) {
        // 遍历所有许可证检查功能是否可用
        for (PluginLicense license : pluginLicenses.values()) {
            ValidationResult result = validateLicense(license);
            if (result.isValid()) {
                // 这里可以扩展更复杂的功能检查逻辑
                // 目前简单返回true表示有有效许可证就启用功能
                return true;
            }
        }
        return false;
    }

    /**
     * 获取验证统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getValidationStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        int totalLicenses = pluginLicenses.size();
        int validLicenses = 0;
        int invalidLicenses = 0;

        for (PluginLicense license : pluginLicenses.values()) {
            ValidationResult result = validateLicense(license);
            if (result.isValid()) {
                validLicenses++;
            } else {
                invalidLicenses++;
            }
        }

        stats.put("totalLicenses", totalLicenses);
        stats.put("validLicenses", validLicenses);
        stats.put("invalidLicenses", invalidLicenses);
        stats.put("validationRate", totalLicenses > 0 ? (double) validLicenses / totalLicenses : 0.0);
        stats.put("initialized", initialized);
        stats.put("lastUpdate", java.time.LocalDateTime.now());

        return stats;
    }

    /**
     * 设置安全验证器（用于依赖注入）
     *
     * @param securityValidator 安全验证器
     */
    @Override
    public void setSecurityValidator(SecurityValidator securityValidator) {
        this.securityValidator = securityValidator;
        this.initialized = false; // 重置初始化状态
    }

    /**
     * 获取安全验证器
     *
     * @return 安全验证器
     */
    public SecurityValidator getSecurityValidator() {
        return securityValidator;
    }

}
