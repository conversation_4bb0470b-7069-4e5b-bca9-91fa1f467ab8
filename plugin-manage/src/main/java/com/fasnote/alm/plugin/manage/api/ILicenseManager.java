package com.fasnote.alm.plugin.manage.api;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader;
import com.fasnote.alm.plugin.manage.model.LicenseInfo;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;
import com.fasnote.alm.plugin.manage.registry.LicenseServiceRegistry;

/**
 * 许可证管理器核心接口
 * 定义许可证管理的主要功能
 */
public interface ILicenseManager {

    /**
     * 激活插件许可证（加载实现类）
     *
     * @param pluginId 插件ID
     * @return 激活结果
     */
    ValidationResult activatePluginLicense(String pluginId);

    /**
     * 检查插件是否有有效许可证
     *
     * @param pluginId 插件ID
     * @return 是否有效
     */
    boolean hasValidLicense(String pluginId);

    /**
     * 检查插件是否有许可证（不验证有效性）
     *
     * @param pluginId 插件ID
     * @return 是否存在许可证
     */
    boolean hasPluginLicense(String pluginId);

    /**
     * 获取许可证信息
     *
     * @param pluginId 插件ID
     * @return 许可证信息的Optional包装，如果不存在则返回empty
     */
    Optional<LicenseInfo> getLicenseInfo(String pluginId);

    /**
     * 获取插件许可证
     *
     * @param pluginId 插件ID
     * @return 许可证对象的Optional包装，如果不存在则返回empty
     */
    Optional<PluginLicense> getPluginLicense(String pluginId);

    /**
     * 获取插件的加密类加载器
     *
     * @param pluginId 插件ID
     * @return 加密类加载器的Optional包装，如果不存在则返回empty
     */
    Optional<EncryptedClassLoader> getEncryptedClassLoader(String pluginId);

    /**
     * 根据服务接口从许可证中创建服务实例
     *
     * @param serviceInterface 服务接口
     * @return 服务实例，如果许可证中没有对应实现则返回null
     */
    Object createServiceInstanceFromLicense(Class<?> serviceInterface);

    /**
     * 获取所有已注册的插件ID
     *
     * @return 插件ID列表
     */
    List<String> getRegisteredPluginIds();

    /**
     * 获取许可证服务注册表
     *
     * @return 服务注册表实例
     */
    LicenseServiceRegistry getServiceRegistry();

    /**
     * 获取统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getStatistics();

    /**
     * 检查功能是否可用
     *
     * @param featureName 功能名称
     * @return 是否可用
     */
    boolean isFeatureEnabled(String featureName);

    /**
     * 移除插件许可证
     *
     * @param pluginId 插件ID
     */
    void removePluginLicense(String pluginId);

    /**
     * 刷新所有许可证状态
     */
    void refreshAllLicenses();

    /**
     * 清理资源
     */
    void cleanup();
}
