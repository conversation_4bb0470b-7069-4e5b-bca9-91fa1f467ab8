package com.fasnote.alm.plugin.manage.core;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.api.IClassLoaderManager;
import com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator;
import com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager;
import com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader;

/**
 * 插件运行时协调器实现
 * 负责协调插件的运行时环境设置和服务注册
 */
public class PluginRuntimeCoordinator implements IPluginRuntimeCoordinator {

    private static final Logger logger = LoggerFactory.getLogger(PluginRuntimeCoordinator.class);

    // 加密类加载器映射（插件ID -> 类加载器）
    private final Map<String, EncryptedClassLoader> classLoaders = new ConcurrentHashMap<>();

    // 类加载器管理器
    private final IClassLoaderManager classLoaderManager;

    // 服务注册管理器
    private final IServiceRegistrationManager serviceRegistrationManager;

    // 统一许可证处理器
    private final UnifiedLicenseProcessor licenseProcessor;

    /**
     * 构造函数
     *
     * @param classLoaderManager         类加载器管理器
     * @param serviceRegistrationManager 服务注册管理器
     * @param licenseProcessor           统一许可证处理器
     */
    public PluginRuntimeCoordinator(IClassLoaderManager classLoaderManager,
                                   IServiceRegistrationManager serviceRegistrationManager,
                                   UnifiedLicenseProcessor licenseProcessor) {
        this.classLoaderManager = classLoaderManager;
        this.serviceRegistrationManager = serviceRegistrationManager;
        this.licenseProcessor = licenseProcessor;
    }

    @Override
    public void setupPluginRuntime(String pluginId, LicenseCache cache) {
        logger.info("设置插件运行时环境: {}", pluginId);

        try {
            // 1. 创建加密类加载器
            Optional<EncryptedClassLoader> encryptedClassLoaderOpt = classLoaderManager.createEncryptedClassLoaderForPlugin(pluginId);

            if (encryptedClassLoaderOpt.isPresent()) {
                EncryptedClassLoader encryptedClassLoader = encryptedClassLoaderOpt.get();
                classLoaders.put(pluginId, encryptedClassLoader);
                logger.info("插件 {} 的加密类加载器已创建并存储", pluginId);

                // 2. 加载并注册许可证中定义的服务实现
                serviceRegistrationManager.loadAndRegisterImplementations(pluginId, cache, encryptedClassLoader);
            } else {
                logger.info("插件 {} 没有加密类，将使用标准类加载方式", pluginId);
            }

            logger.info("插件运行时环境设置完成: {}", pluginId);
        } catch (Exception e) {
            logger.error("设置插件运行时环境失败: {}", pluginId, e);
        }
    }

    @Override
    public void setupPluginRuntime(String pluginId, byte[] rawLicenseData) {
        // 优先使用缓存中的数据
        LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
        if (cache != null) {
            logger.info("使用缓存数据设置插件运行时环境: {}", pluginId);
            setupPluginRuntime(pluginId, cache);
        } else {
            // 兼容性处理：如果缓存不可用，尝试从许可证数据重新处理
            logger.info("缓存不可用，使用原始许可证数据设置插件运行时环境: {}", pluginId);
            try {
                // 重新处理许可证数据并缓存
                com.fasnote.alm.plugin.manage.model.ValidationResult result = licenseProcessor.loadAndProcessLicense(pluginId, rawLicenseData);
                if (result.isValid()) {
                    cache = licenseProcessor.getCachedLicense(pluginId);
                    if (cache != null) {
                        setupPluginRuntime(pluginId, cache);
                    }
                } else {
                    logger.error("重新处理许可证数据失败: {}, 原因: {}", pluginId, result.getMessage());
                }
            } catch (Exception e) {
                logger.error("从原始许可证数据设置插件运行时环境失败: {}", pluginId, e);
            }
        }
    }

    @Override
    public Optional<EncryptedClassLoader> getPluginClassLoader(String pluginId) {
        return Optional.ofNullable(classLoaders.get(pluginId));
    }

    @Override
    public void cleanupPluginRuntime(String pluginId) {
        logger.info("清理插件运行时环境: {}", pluginId);

        try {
            // 1. 清理类加载器
            EncryptedClassLoader classLoader = classLoaders.remove(pluginId);
            if (classLoader != null) {
                classLoader.clearCache();
                logger.info("已清理插件类加载器: {}", pluginId);
            }

            // 2. 清理服务注册
            serviceRegistrationManager.cleanupPluginServices(pluginId);

            logger.info("插件运行时环境清理完成: {}", pluginId);

        } catch (Exception e) {
            logger.error("清理插件运行时环境失败: {}", pluginId, e);
        }
    }

    @Override
    public void cleanupAllPluginRuntimes() {
        logger.info("清理所有插件运行时环境");

        try {
            // 1. 清理所有类加载器
            for (Map.Entry<String, EncryptedClassLoader> entry : classLoaders.entrySet()) {
                String pluginId = entry.getKey();
                EncryptedClassLoader classLoader = entry.getValue();
                try {
                    classLoader.clearCache();
                    logger.info("已清理插件类加载器: {}", pluginId);
                } catch (Exception e) {
                    logger.error("清理插件类加载器失败: {}", pluginId, e);
                }
            }
            classLoaders.clear();

            // 2. 清理所有服务注册
            serviceRegistrationManager.cleanupAllServices();

            logger.info("所有插件运行时环境清理完成");

        } catch (Exception e) {
            logger.error("清理所有插件运行时环境失败", e);
        }
    }

    @Override
    public boolean ensurePluginRuntimeInitialized(String pluginId) {
        if (hasPluginRuntime(pluginId)) {
            return true;
        }

        logger.info("插件运行时环境未初始化，尝试自动初始化: {}", pluginId);

        try {
            // 尝试从缓存中获取许可证数据
            LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
            if (cache != null) {
                setupPluginRuntime(pluginId, cache);
                return hasPluginRuntime(pluginId);
            } else {
                logger.warn("无法找到插件 {} 的许可证缓存，无法自动初始化运行时环境", pluginId);
                return false;
            }
        } catch (Exception e) {
            logger.error("自动初始化插件运行时环境失败: {}", pluginId, e);
            return false;
        }
    }

    @Override
    public boolean hasPluginRuntime(String pluginId) {
        return classLoaders.containsKey(pluginId);
    }

    @Override
    public int getPluginRuntimeCount() {
        return classLoaders.size();
    }
}
