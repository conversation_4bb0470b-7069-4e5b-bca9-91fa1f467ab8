package com.fasnote.alm.plugin.manage.injection.module;

import org.osgi.framework.BundleContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IBinder;
import com.fasnote.alm.injection.api.IInjectionContext;
import com.fasnote.alm.injection.api.IModule;
import com.fasnote.alm.injection.api.IServiceProvider;
import com.fasnote.alm.injection.facade.DI;
import com.fasnote.alm.plugin.manage.Activator;
import com.fasnote.alm.plugin.manage.api.IBundleManager;
import com.fasnote.alm.plugin.manage.api.IClassLoaderManager;
import com.fasnote.alm.plugin.manage.api.ILicenseFileManager;
import com.fasnote.alm.plugin.manage.api.ILicenseManager;
import com.fasnote.alm.plugin.manage.api.ILicenseValidator;
import com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager;
import com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager;
import com.fasnote.alm.plugin.manage.api.LicenseAware;
import com.fasnote.alm.plugin.manage.core.BundleManager;
import com.fasnote.alm.plugin.manage.core.ClassLoaderManager;
import com.fasnote.alm.plugin.manage.core.LicenseFileManager;
import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.core.LicenseValidator;
import com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager;
import com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager;
import com.fasnote.alm.plugin.manage.core.UnifiedLicenseProcessor;
import com.fasnote.alm.plugin.manage.injection.resolver.LicenseAwareServiceResolver;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.registry.LicenseServiceRegistry;

/**
 * 统一许可证模块
 *
 * 整合了 LicenseManager 的依赖注入配置和许可证感知服务功能
 *
 * 核心功能：
 * 1. 配置 LicenseManager 的依赖注入（单例模式）
 * 2. 注册许可证感知服务拦截器
 * 3. 根据许可证状态选择 LicenseImplementation 或 FallbackImplementation
 * 4. 支持许可证动态更新时的服务切换
 */
public class LicenseModule implements IModule {

	/**
	 * BundleContext 服务提供者
	 */
	private class BundleContextProvider implements IServiceProvider<BundleContext> {
		@Override
		public BundleContext provide(IInjectionContext context) {
			BundleContext bundleContext = Activator.getContext();
			if (bundleContext == null) {
				throw new RuntimeException("BundleContext不可用");
			}
			logger.debug("提供BundleContext实例: {}", bundleContext.getBundle().getSymbolicName());
			return bundleContext;
		}
	}

	/**
	 * LicenseManager 服务提供者（使用依赖注入）
	 */
	private class LicenseManagerProvider implements IServiceProvider<LicenseManager> {

		@Override
		public LicenseManager provide(IInjectionContext context) {
			logger.debug("创建LicenseManager实例（依赖注入模式）...");

			try {
				// 从依赖注入容器获取所有依赖
				BundleContext bundleContext = context.getService(BundleContext.class);
				IBundleManager bundleManager = context.getService(IBundleManager.class);
				IClassLoaderManager classLoaderManager = context.getService(IClassLoaderManager.class);
				ILicenseValidator licenseValidator = context.getService(ILicenseValidator.class);
				IServiceRegistrationManager serviceRegistrationManager = context.getService(IServiceRegistrationManager.class);
				IRuntimeEnvironmentManager runtimeEnvironmentManager = context.getService(IRuntimeEnvironmentManager.class);
				ILicenseFileManager licenseFileManager = context.getService(ILicenseFileManager.class);
				UnifiedLicenseProcessor licenseProcessor = context.getService(UnifiedLicenseProcessor.class);
				LicenseServiceRegistry serviceRegistry = context.getService(LicenseServiceRegistry.class);

				// 使用构造函数注入创建LicenseManager
				LicenseManager licenseManager = new LicenseManager(
					bundleContext,
					bundleManager,
					classLoaderManager,
					licenseValidator,
					serviceRegistrationManager,
					runtimeEnvironmentManager,
					licenseFileManager,
					licenseProcessor,
					serviceRegistry
				);

				logger.info("LicenseManager实例创建成功（依赖注入模式）");
				return licenseManager;
			} catch (Exception e) {
				logger.error("创建LicenseManager实例失败", e);
				throw new RuntimeException("无法创建LicenseManager实例", e);
			}
		}
	}

	/**
	 * 许可证感知服务提供者
	 */
	private class LicenseAwareProvider implements IServiceProvider<LicenseAware> {

		@Override
		public LicenseAware provide(IInjectionContext context) {
			// 返回一个简单的许可证信息访问器
			return new LicenseAware() {
				private PluginLicense license;

				@Override
				public PluginLicense getLicenseInfo() {
					return license;
				}

				@Override
				public void setLicenseInfo(PluginLicense license) {
					this.license = license;
				}
			};
		}
	}

	private static final Logger logger = LoggerFactory.getLogger(LicenseModule.class);
	private LicenseManager licenseManager;
	private LicenseAwareServiceResolver licenseAwareResolver;

	/**
	 * 无参构造函数（用于依赖注入框架自动扫描）
	 *
	 * 模块注册的设计原则：
	 * 1. 模块应该是配置单元，不应该依赖外部状态
	 * 2. 模块的依赖应该通过 configure() 方法中的依赖查找获取
	 * 3. 这样可以确保模块的可重用性和测试性
	 */
	public LicenseModule() {
		logger.info("LicenseModule使用无参构造函数创建");
	}

	@Override
	public void configure(IBinder binder) {
		logger.info("配置统一许可证模块（重构后）...");

		// 1. 注册 LicenseManager 为单例服务（重构后的协调器）
		logger.info("开始配置LicenseManager依赖注入（重构后的协调器模式）...");
		binder.bind(LicenseManager.class).toProvider(new LicenseManagerProvider()).asSingleton().build();

		// 2. 注册 ILicenseManager 接口绑定到 LicenseManager 实现
		binder.bind(ILicenseManager.class, LicenseManager.class).asSingleton().build();
		logger.info("LicenseManager依赖注入配置完成");

		// 获取刚注册的 LicenseManager 实例
		this.licenseManager = DI.get(LicenseManager.class);
		if (this.licenseManager == null) {
			throw new IllegalStateException("无法获取 LicenseManager 实例");
		}
		logger.debug("成功获取 LicenseManager 实例");

		// 3. 注册许可证感知服务解析器
		this.licenseAwareResolver = new LicenseAwareServiceResolver(licenseManager);
		binder.registerServiceResolver(licenseAwareResolver);
		logger.info("许可证感知服务解析器已注册，优先级: {}", licenseAwareResolver.getPriority());

		// 4. 注册许可证感知服务提供者
		binder.bind(LicenseAware.class).toProvider(new LicenseAwareProvider()).build();

		// 5. 注册基础组件（如果需要单独访问）
		registerCoreComponents(binder);

		logger.info("统一许可证模块配置完成（重构后）");
	}

	/**
	 * 注册核心组件（可选，用于需要直接访问特定管理器的场景）
	 */
	private void registerCoreComponents(IBinder binder) {
		logger.debug("注册核心组件...");

		// 注册统一许可证处理器
		binder.bind(UnifiedLicenseProcessor.class).asSingleton().build();

		// 注册许可证服务注册表
		binder.bind(LicenseServiceRegistry.class).asSingleton().build();

		logger.debug("核心组件注册完成");
	}

	/**
	 * 清理实现类缓存（支持许可证动态更新）
	 *
	 * @param serviceInterface 要清理的服务接口，如果为null则清理所有缓存
	 */
	public void clearImplementationCache(Class<?> serviceInterface) {
		if (licenseAwareResolver != null) {
			licenseAwareResolver.clearImplementationCache(serviceInterface);
			logger.info("已清理许可证实现缓存: {}", serviceInterface != null ? serviceInterface.getName() : "全部");
		}
	}

	@Override
	public String getName() {
		return "RefactoredLicenseModule";
	}

	@Override
	public int getPriority() {
		return 5; // 更高优先级，确保重构后的 LicenseManager 在其他许可证相关模块之前初始化
	}







	/**
	 * 基于接口名称提供服务 许可证验证系统的核心方法，支持动态服务查找
	 *
	 * @param interfaceName 接口全限定名称
	 * @param context       注入上下文
	 * @return 服务实例，如果未找到返回null
	 */
	public Object provideServiceByInterfaceName(String interfaceName, IInjectionContext context) {
		if (licenseManager == null) {
			logger.debug("LicenseManager未初始化");
			return null;
		}

		logger.debug("基于接口名称查找许可证服务: {}", interfaceName);

		try {
			// 尝试通过接口名称加载接口类
			Class<?> interfaceClass = Class.forName(interfaceName);

			// 使用LicenseManager创建服务实例
			Object instance = licenseManager.createServiceInstanceFromLicense(interfaceClass);
			if (instance != null) {
				logger.info("成功通过接口名称创建服务实例: {}", interfaceName);
				return instance;
			}
		} catch (ClassNotFoundException e) {
			logger.warn("无法加载接口类: {}", interfaceName, e);
		} catch (Exception e) {
			logger.error("通过接口名称创建服务实例失败: {}", interfaceName, e);
		}

		logger.debug("未找到匹配的许可证服务: {}", interfaceName);
		return null;
	}

}