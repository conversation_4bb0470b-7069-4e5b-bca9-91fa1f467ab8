package com.fasnote.alm.plugin.manage.monitor;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.config.LicenseConfiguration;
import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.model.ValidationResult;

/**
 * 框架监控器 职责：系统监控和健康检查
 *
 * 功能： - 统计信息收集 - 健康检查 - 状态监控
 */
public class FrameworkMonitor {

	private static final Logger logger = LoggerFactory.getLogger(FrameworkMonitor.class);

	private final LicenseManager licenseManager;
	private final LicenseConfiguration configuration;

	public FrameworkMonitor() {
		// TODO: 现在LicenseManager只支持依赖注入构造函数
		// 需要通过DI容器获取LicenseManager实例
		this.licenseManager = null; // 暂时设为null
		this.configuration = LicenseConfiguration.getInstance();
	}

	/**
	 * 构造函数（依赖注入）
	 */
	public FrameworkMonitor(LicenseManager licenseManager, LicenseConfiguration configuration) {
		this.licenseManager = licenseManager;
		this.configuration = configuration;
	}

	/**
	 * 获取框架描述
	 *
	 * @return 框架描述
	 */
	public String getDescription() {
		return "轻量级许可证验证和服务管理框架";
	}

	/**
	 * 获取框架统计信息
	 *
	 * @return 统计信息
	 */
	public Map<String, Object> getFrameworkStatistics() {
		Map<String, Object> stats = new java.util.HashMap<>();

		// 许可证统计
		stats.put("totalLicenses", licenseManager.getRegisteredPluginIds().size());

		// 配置统计
		stats.put("configurationLoaded", true);

		// 系统信息
		stats.put("frameworkVersion", "2.0.0");
		stats.put("javaVersion", System.getProperty("java.version"));
		stats.put("osName", System.getProperty("os.name"));

		return stats;
	}

	/**
	 * 获取框架版本
	 *
	 * @return 版本号
	 */
	public String getVersion() {
		return "2.0.0";
	}

	/**
	 * 健康检查 检查框架各组件是否正常运行
	 *
	 * @return 健康检查结果
	 */
	public ValidationResult healthCheck() {
		try {
			// 检查各组件状态

			// 检查配置
			if (configuration == null) {
				return ValidationResult.failure("配置管理器未初始化");
			}

			// 检查许可证管理器
			if (licenseManager == null) {
				return ValidationResult.failure("许可证管理器未初始化");
			}

			return ValidationResult.success("框架健康检查通过");

		} catch (Exception e) {
			logger.error("健康检查失败", e);
			return ValidationResult.failure("健康检查异常: " + e.getMessage());
		}
	}
}
