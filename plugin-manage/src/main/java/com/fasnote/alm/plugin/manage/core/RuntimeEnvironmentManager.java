package com.fasnote.alm.plugin.manage.core;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.api.IClassLoaderManager;
import com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager;
import com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager;
import com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader;

/**
 * 运行时环境管理器
 * 负责设置和管理插件的运行时环境，包括类加载器创建和服务注册
 */
public class RuntimeEnvironmentManager implements IRuntimeEnvironmentManager {

    private static final Logger logger = LoggerFactory.getLogger(RuntimeEnvironmentManager.class);

    // 加密类加载器映射（插件ID -> 类加载器）
    private final Map<String, EncryptedClassLoader> classLoaders = new ConcurrentHashMap<>();

    // 类加载器管理器
    private final IClassLoaderManager classLoaderManager;

    // 服务注册管理器
    private final IServiceRegistrationManager serviceRegistrationManager;

    // 统一许可证处理器
    private final UnifiedLicenseProcessor licenseProcessor;

    /**
     * 构造函数
     *
     * @param classLoaderManager         类加载器管理器
     * @param serviceRegistrationManager 服务注册管理器
     * @param licenseProcessor           统一许可证处理器
     */
    public RuntimeEnvironmentManager(IClassLoaderManager classLoaderManager,
                                     IServiceRegistrationManager serviceRegistrationManager,
                                     UnifiedLicenseProcessor licenseProcessor) {
        this.classLoaderManager = classLoaderManager;
        this.serviceRegistrationManager = serviceRegistrationManager;
        this.licenseProcessor = licenseProcessor;
    }

    /**
     * 从许可证缓存设置运行时环境（主要实现）
     *
     * @param pluginId 插件ID
     * @param cache    许可证缓存
     */
    @Override
    public void setupRuntimeEnvironment(String pluginId, LicenseCache cache) {
        try {
            logger.info("从缓存设置运行时环境: {}", pluginId);

            // 检查是否有加密类需要加载
            if (!cache.hasEncryptedClasses()) {
                logger.info("插件 {} 没有加密类，将使用标准类加载方式", pluginId);
                return;
            }

            // 创建加密类加载器
            Optional<EncryptedClassLoader> encryptedClassLoaderOpt = classLoaderManager.createEncryptedClassLoaderForPlugin(pluginId);

            if (encryptedClassLoaderOpt.isPresent()) {
                EncryptedClassLoader encryptedClassLoader = encryptedClassLoaderOpt.get();
                classLoaders.put(pluginId, encryptedClassLoader);
                logger.info("插件 {} 的加密类加载器已创建并存储", pluginId);

                // 加载并注册许可证中定义的服务实现
                serviceRegistrationManager.loadAndRegisterImplementations(pluginId, cache, encryptedClassLoader);
            } else {
                logger.info("插件 {} 没有加密类，将使用标准类加载方式", pluginId);
            }

            logger.info("运行时环境设置完成: {}", pluginId);
        } catch (Exception e) {
            logger.error("设置运行时环境失败: {}", pluginId, e);
        }
    }

    /**
     * 从原始许可证数据设置运行时环境（兼容性方法）
     *
     * @param pluginId        插件ID
     * @param rawLicenseData  原始许可证数据
     */
    @Override
    public void setupRuntimeEnvironment(String pluginId, byte[] rawLicenseData) {
        // 优先使用缓存中的数据
        LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
        if (cache != null) {
            logger.info("使用缓存数据设置运行时环境: {}", pluginId);
            setupRuntimeEnvironment(pluginId, cache);
        } else {
            // 兼容性处理：如果缓存不可用，尝试从许可证数据重新处理
            logger.info("缓存不可用，使用原始许可证数据设置运行时环境: {}", pluginId);
            try {
                // 重新处理许可证数据并缓存
                com.fasnote.alm.plugin.manage.model.ValidationResult result = licenseProcessor.loadAndProcessLicense(pluginId, rawLicenseData);
                if (result.isValid()) {
                    cache = licenseProcessor.getCachedLicense(pluginId);
                    if (cache != null) {
                        setupRuntimeEnvironment(pluginId, cache);
                    }
                } else {
                    logger.error("重新处理许可证数据失败: {}, 原因: {}", pluginId, result.getMessage());
                }
            } catch (Exception e) {
                logger.error("从原始许可证数据设置运行时环境失败: {}", pluginId, e);
            }
        }
    }

    /**
     * 设置运行时环境 从许可证数据设置运行时环境（兼容性包装器）
     * 内部优先使用缓存，如果缓存不可用则解析许可证数据
     *
     * @param pluginId    插件ID
     * @param licenseData 许可证数据
     */
    public void setupRuntimeEnvironment(String pluginId, String licenseData) {
        // 优先使用缓存中的数据
        LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
        if (cache != null) {
            logger.info("使用缓存数据设置运行时环境: {}", pluginId);
            setupRuntimeEnvironment(pluginId, cache);
        } else {
            // 兼容性处理：如果缓存不可用，尝试从许可证数据重新处理
            logger.info("缓存不可用，使用许可证数据设置运行时环境: {}", pluginId);
            try {
                // 重新处理许可证数据并缓存
                com.fasnote.alm.plugin.manage.model.ValidationResult result = licenseProcessor.loadAndProcessLicense(pluginId,
                        licenseData.getBytes("UTF-8"));
                if (result.isValid()) {
                    cache = licenseProcessor.getCachedLicense(pluginId);
                    if (cache != null) {
                        setupRuntimeEnvironment(pluginId, cache);
                    }
                } else {
                    logger.error("重新处理许可证数据失败: {}, 原因: {}", pluginId, result.getMessage());
                }
            } catch (Exception e) {
                logger.error("设置运行时环境失败: {}", pluginId, e);
            }
        }
    }

    /**
     * 确保插件的运行时环境已经初始化
     * 如果没有初始化，则自动进行初始化
     *
     * @param pluginId 插件ID
     * @return 是否成功初始化或已经初始化
     */
    public boolean ensureRuntimeEnvironmentInitialized(String pluginId) {
        // 检查是否已经有加密类加载器
        Optional<EncryptedClassLoader> classLoaderOpt = getEncryptedClassLoader(pluginId);
        if (classLoaderOpt.isPresent()) {
            return true; // 已经初始化
        }

        // 尝试自动初始化
        try {
            logger.info("检测到插件运行时环境未初始化，开始自动初始化: {}", pluginId);

            // 优先使用缓存中的数据
            LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
            if (cache != null) {
                setupRuntimeEnvironment(pluginId, cache);
            } else {
                logger.warn("无法自动初始化运行时环境：缓存数据不可用: {}", pluginId);
                return false;
            }

            // 验证初始化是否成功
            classLoaderOpt = getEncryptedClassLoader(pluginId);
            boolean success = classLoaderOpt.isPresent();

            if (success) {
                logger.info("成功自动初始化插件运行时环境: {}", pluginId);
            } else {
                logger.warn("自动初始化插件运行时环境失败: {}", pluginId);
            }

            return success;

        } catch (Exception e) {
            logger.error("自动初始化插件运行时环境异常: {}", pluginId, e);
            return false;
        }
    }

    /**
     * 获取插件的加密类加载器
     *
     * @param pluginId 插件ID
     * @return 加密类加载器的Optional包装，如果不存在则返回empty
     */
    @Override
    public Optional<EncryptedClassLoader> getEncryptedClassLoader(String pluginId) {
        if (pluginId == null || pluginId.trim().isEmpty()) {
            logger.warn("插件ID为空，无法获取类加载器");
            return Optional.empty();
        }

        EncryptedClassLoader classLoader = classLoaders.get(pluginId);
        if (classLoader == null) {
            logger.debug("未找到插件的加密类加载器: {}", pluginId);
        }

        return Optional.ofNullable(classLoader);
    }

    /**
     * 清理指定插件的运行时环境
     *
     * @param pluginId 插件ID
     */
    @Override
    public void cleanupRuntimeEnvironment(String pluginId) {
        logger.info("清理插件运行时环境: {}", pluginId);

        try {
            // 清理类加载器
            EncryptedClassLoader classLoader = classLoaders.remove(pluginId);
            if (classLoader != null) {
                classLoader.clearCache();
                logger.info("已清理插件类加载器: {}", pluginId);
            }

            // 清理服务注册
            serviceRegistrationManager.cleanupPluginServices(pluginId);

            logger.info("插件运行时环境清理完成: {}", pluginId);

        } catch (Exception e) {
            logger.error("清理插件运行时环境失败: {}", pluginId, e);
        }
    }

    /**
     * 清理所有运行时环境
     */
    @Override
    public void cleanupAllRuntimeEnvironments() {
        logger.info("清理所有运行时环境");

        try {
            // 清理所有类加载器
            for (Map.Entry<String, EncryptedClassLoader> entry : classLoaders.entrySet()) {
                String pluginId = entry.getKey();
                EncryptedClassLoader classLoader = entry.getValue();
                try {
                    classLoader.clearCache();
                    logger.info("已清理插件类加载器: {}", pluginId);
                } catch (Exception e) {
                    logger.error("清理插件类加载器失败: {}", pluginId, e);
                }
            }
            classLoaders.clear();

            // 清理所有服务注册
            serviceRegistrationManager.cleanupAllServices();

            logger.info("所有运行时环境清理完成");

        } catch (Exception e) {
            logger.error("清理所有运行时环境失败", e);
        }
    }

    /**
     * 获取已管理的运行时环境数量
     *
     * @return 运行时环境数量
     */
    @Override
    public int getRuntimeEnvironmentCount() {
        return classLoaders.size();
    }

    /**
     * 检查插件是否有运行时环境
     *
     * @param pluginId 插件ID
     * @return 是否存在运行时环境
     */
    @Override
    public boolean hasRuntimeEnvironment(String pluginId) {
        return classLoaders.containsKey(pluginId);
    }
}
