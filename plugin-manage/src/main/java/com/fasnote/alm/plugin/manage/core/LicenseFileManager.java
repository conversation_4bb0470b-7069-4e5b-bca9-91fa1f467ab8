package com.fasnote.alm.plugin.manage.core;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.api.ILicenseFileManager;
import com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;
import com.fasnote.alm.plugin.manage.util.LicenseDirectoryManager;

/**
 * 许可证文件管理器实现
 * 负责许可证文件的扫描、加载、预加载功能
 */
public class LicenseFileManager implements ILicenseFileManager {

    private static final Logger logger = LoggerFactory.getLogger(LicenseFileManager.class);

    // 已加载的插件许可证缓存
    private final Map<String, PluginLicense> pluginLicenses;

    // 统一许可证处理器
    private final UnifiedLicenseProcessor licenseProcessor;

    // 运行时环境管理器
    private final IRuntimeEnvironmentManager runtimeEnvironmentManager;

    /**
     * 构造函数
     *
     * @param pluginLicenses              插件许可证缓存
     * @param licenseProcessor            统一许可证处理器
     * @param runtimeEnvironmentManager   运行时环境管理器
     */
    public LicenseFileManager(Map<String, PluginLicense> pluginLicenses,
                              UnifiedLicenseProcessor licenseProcessor,
                              IRuntimeEnvironmentManager runtimeEnvironmentManager) {
        this.pluginLicenses = pluginLicenses;
        this.licenseProcessor = licenseProcessor;
        this.runtimeEnvironmentManager = runtimeEnvironmentManager;
    }

    @Override
    public void autoScanAndLoadLicenses() {
        try {
            logger.info("开始自动扫描许可证文件");

            // 获取许可证目录
            java.io.File licenseDir = LicenseDirectoryManager.getInstance().getLicenseDir();

            // 扫描许可证文件
            LicenseScanner scanner = new LicenseScanner(licenseDir);
            Map<String, String> licenseFiles = scanner.scanLicenseFiles();

            // 预加载每个许可证文件（仅解密验证，不加载实现类）
            for (Map.Entry<String, String> entry : licenseFiles.entrySet()) {
                String pluginId = entry.getKey();
                String licenseFilePath = entry.getValue();

                try {
                    preloadLicenseFromFile(pluginId, licenseFilePath);
                } catch (Exception e) {
                    logger.error("预加载许可证文件失败: {}", licenseFilePath, e);
                }
            }

            logger.info("自动扫描许可证文件完成，共预加载 {} 个许可证", licenseFiles.size());

        } catch (Exception e) {
            logger.error("自动扫描许可证文件失败", e);
        }
    }

    @Override
    public ValidationResult loadAndActivateLicenseFromFile(String pluginId) {
        try {
            logger.info("从文件加载并激活许可证: {}", pluginId);

            // 获取许可证目录
            java.io.File licenseDir = LicenseDirectoryManager.getInstance().getLicenseDir();

            // 扫描许可证文件
            LicenseScanner scanner = new LicenseScanner(licenseDir);
            Map<String, String> licenseFiles = scanner.scanLicenseFiles();

            String licenseFilePath = licenseFiles.get(pluginId);
            if (licenseFilePath == null) {
                return ValidationResult.failure("未找到许可证文件: " + pluginId);
            }

            // 从文件加载许可证
            loadLicenseFromFile(pluginId, licenseFilePath);

            return ValidationResult.success("许可证加载并激活成功");

        } catch (Exception e) {
            String errorMessage = "从文件加载并激活许可证失败: " + e.getMessage();
            logger.error(errorMessage, e);
            return ValidationResult.failure(errorMessage);
        }
    }

    @Override
    public ValidationResult registerPluginLicense(String pluginId, byte[] licenseData) {
        logger.info("注册插件许可证: {}", pluginId);

        // 直接使用统一处理器
        ValidationResult result = licenseProcessor.loadAndProcessLicense(pluginId, licenseData);
        if (result.isValid()) {
            // 从缓存中获取处理结果并存储到本地缓存
            LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
            if (cache != null) {
                pluginLicenses.put(pluginId, cache.getLicenseMetadata());

                // 设置运行时环境（加载实现类）
                runtimeEnvironmentManager.setupRuntimeEnvironment(pluginId, cache);

                logger.info("插件许可证注册成功: {}", pluginId);
            }
        } else {
            logger.warn("插件许可证注册失败: {}, 原因: {}", pluginId, result.getMessage());
        }

        return result;
    }

    @Override
    public ValidationResult preloadPluginLicense(String pluginId, byte[] licenseData) {
        logger.info("预加载插件许可证: {}", pluginId);

        // 直接使用统一处理器
        ValidationResult result = licenseProcessor.loadAndProcessLicense(pluginId, licenseData);
        if (result.isValid()) {
            // 从缓存中获取处理结果并存储到本地缓存
            LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
            if (cache != null) {
                pluginLicenses.put(pluginId, cache.getLicenseMetadata());

                // 设置运行时环境（创建类加载器）
                // 这对于许可证拦截器正常工作是必需的
                runtimeEnvironmentManager.setupRuntimeEnvironment(pluginId, cache);

                logger.info("插件许可证预加载成功: {}", pluginId);
            }
        } else {
            logger.warn("插件许可证预加载失败: {}, 原因: {}", pluginId, result.getMessage());
        }

        return result;
    }

    @Override
    public ValidationResult preloadPluginLicenseFromFile(String pluginId, String licenseFilePath) {
        logger.info("从文件预加载插件许可证: {} -> {}", pluginId, licenseFilePath);

        // 直接使用统一处理器
        ValidationResult result = licenseProcessor.loadAndProcessLicense(pluginId, licenseFilePath);
        if (result.isValid()) {
            // 从缓存中获取处理结果并存储到本地缓存
            LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
            if (cache != null) {
                pluginLicenses.put(pluginId, cache.getLicenseMetadata());

                // 设置运行时环境（创建类加载器）
                // 这对于许可证拦截器正常工作是必需的
                runtimeEnvironmentManager.setupRuntimeEnvironment(pluginId, cache);

                logger.info("插件许可证预加载成功: {}", pluginId);
            }
        } else {
            logger.warn("插件许可证预加载失败: {}, 原因: {}", pluginId, result.getMessage());
        }

        return result;
    }

    @Override
    public int rescanLicenseFiles() {
        try {
            logger.info("重新扫描许可证文件");

            // 获取许可证目录
            java.io.File licenseDir = LicenseDirectoryManager.getInstance().getLicenseDir();

            // 创建扫描器并获取当前已加载的许可证
            LicenseScanner scanner = new LicenseScanner(licenseDir);
            Map<String, String> currentLicenses = new HashMap<>();
            for (String pluginId : pluginLicenses.keySet()) {
                currentLicenses.put(pluginId, ""); // 简化处理，只关心插件ID
            }

            // 扫描新的许可证文件
            Map<String, String> newLicenseFiles = scanner.refreshScan(currentLicenses);

            // 加载新发现的许可证文件
            int loadedCount = 0;
            for (Map.Entry<String, String> entry : newLicenseFiles.entrySet()) {
                String pluginId = entry.getKey();
                String licenseFilePath = entry.getValue();

                try {
                    loadLicenseFromFile(pluginId, licenseFilePath);
                    loadedCount++;
                } catch (Exception e) {
                    logger.error("重新加载许可证文件失败: {}", licenseFilePath, e);
                }
            }

            logger.info("重新扫描完成，新加载 {} 个许可证文件", loadedCount);
            return loadedCount;

        } catch (Exception e) {
            logger.error("重新扫描许可证文件失败", e);
            return 0;
        }
    }

    @Override
    public Map<String, String> scanLicenseFiles() {
        try {
            // 获取许可证目录
            java.io.File licenseDir = LicenseDirectoryManager.getInstance().getLicenseDir();

            // 扫描许可证文件
            LicenseScanner scanner = new LicenseScanner(licenseDir);
            return scanner.scanLicenseFiles();

        } catch (Exception e) {
            logger.error("扫描许可证文件失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 从文件加载许可证（完整加载，包括实现类）
     *
     * @param pluginId        插件ID
     * @param licenseFilePath 许可证文件路径
     */
    private void loadLicenseFromFile(String pluginId, String licenseFilePath) {
        try {
            logger.info("从文件加载许可证: {} -> {}", pluginId, licenseFilePath);

            // 使用统一处理器进行许可证处理
            ValidationResult result = licenseProcessor.loadAndProcessLicense(pluginId, licenseFilePath);
            if (result.isValid()) {
                // 从缓存中获取处理结果
                LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
                if (cache != null) {
                    // 存储许可证元数据
                    pluginLicenses.put(pluginId, cache.getLicenseMetadata());

                    // 设置运行时环境（加载实现类）
                    runtimeEnvironmentManager.setupRuntimeEnvironment(pluginId, cache);

                    logger.info("成功从文件加载许可证: {}", pluginId);
                }
            } else {
                logger.warn("从文件加载许可证失败: {}, 原因: {}", pluginId, result.getMessage());
            }

        } catch (Exception e) {
            logger.error("从文件加载许可证异常: {}", pluginId, e);
        }
    }

    /**
     * 预加载许可证文件（仅解密验证，不加载实现类）
     *
     * @param pluginId        插件ID
     * @param licenseFilePath 许可证文件路径
     */
    private void preloadLicenseFromFile(String pluginId, String licenseFilePath) {
        try {
            logger.info("预加载许可证文件: {} -> {}", pluginId, licenseFilePath);

            // 使用统一处理器进行许可证处理
            ValidationResult result = licenseProcessor.loadAndProcessLicense(pluginId, licenseFilePath);
            if (result.isValid()) {
                // 从缓存中获取处理结果并存储到本地缓存
                LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
                if (cache != null) {
                    pluginLicenses.put(pluginId, cache.getLicenseMetadata());
                    logger.info("成功预加载许可证: {}", pluginId);
                }
            } else {
                logger.warn("预加载许可证失败: {}, 原因: {}", pluginId, result.getMessage());
            }

        } catch (Exception e) {
            logger.error("预加载许可证异常: {}", pluginId, e);
        }
    }
}
