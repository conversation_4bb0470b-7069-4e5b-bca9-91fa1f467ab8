package com.fasnote.alm.plugin.manage.injection.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.facade.DI;
import com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager;
import com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager;

/**
 * 依赖关系连接服务
 * 负责在所有组件创建完成后设置复杂的依赖关系
 * 
 * 这个服务专门解决循环依赖问题：
 * - ServiceRegistrationManager 需要 RuntimeEnvironmentManager
 * - RuntimeEnvironmentManager 需要 ServiceRegistrationManager
 */
public class DependencyWiringService {

    private static final Logger logger = LoggerFactory.getLogger(DependencyWiringService.class);

    private volatile boolean wired = false;

    /**
     * 连接所有组件的依赖关系
     * 这个方法应该在所有组件都创建完成后调用
     */
    public void wireAllDependencies() {
        if (wired) {
            logger.debug("依赖关系已经连接，跳过重复操作");
            return;
        }

        logger.info("开始连接组件依赖关系...");

        try {
            // 连接ServiceRegistrationManager和RuntimeEnvironmentManager的循环依赖
            wireServiceRegistrationManagerDependencies();

            // 这里可以添加其他需要后置连接的依赖关系
            // wireOtherDependencies();

            wired = true;
            logger.info("所有组件依赖关系连接完成");

        } catch (Exception e) {
            logger.error("连接依赖关系失败", e);
            throw new RuntimeException("依赖关系连接失败", e);
        }
    }

    /**
     * 连接ServiceRegistrationManager的依赖关系
     */
    private void wireServiceRegistrationManagerDependencies() {
        logger.debug("连接ServiceRegistrationManager的依赖关系...");

        // 获取ServiceRegistrationManager实例
        ServiceRegistrationManager serviceManager = DI.get(ServiceRegistrationManager.class);
        if (serviceManager == null) {
            throw new IllegalStateException("无法获取ServiceRegistrationManager实例");
        }

        // 获取RuntimeEnvironmentManager实例
        IRuntimeEnvironmentManager runtimeManager = DI.get(IRuntimeEnvironmentManager.class);
        if (runtimeManager == null) {
            throw new IllegalStateException("无法获取RuntimeEnvironmentManager实例");
        }

        // 设置依赖关系
        serviceManager.setRuntimeEnvironmentManager(runtimeManager);

        logger.debug("ServiceRegistrationManager依赖关系连接完成");
    }

    /**
     * 验证所有依赖关系是否正确连接
     * 
     * @return true表示依赖关系正确，false表示存在问题
     */
    public boolean validateDependencies() {
        if (!wired) {
            logger.warn("依赖关系尚未连接");
            return false;
        }

        try {
            // 验证ServiceRegistrationManager的依赖
            ServiceRegistrationManager serviceManager = DI.get(ServiceRegistrationManager.class);
            IRuntimeEnvironmentManager runtimeManager = DI.get(IRuntimeEnvironmentManager.class);

            if (serviceManager == null || runtimeManager == null) {
                logger.warn("关键组件实例为null");
                return false;
            }

            // 这里可以添加更多的验证逻辑
            logger.debug("依赖关系验证通过");
            return true;

        } catch (Exception e) {
            logger.error("依赖关系验证失败", e);
            return false;
        }
    }

    /**
     * 检查依赖关系是否已连接
     * 
     * @return true表示已连接，false表示未连接
     */
    public boolean isWired() {
        return wired;
    }

    /**
     * 重置连接状态（主要用于测试）
     */
    public void reset() {
        wired = false;
        logger.debug("依赖连接状态已重置");
    }
}
